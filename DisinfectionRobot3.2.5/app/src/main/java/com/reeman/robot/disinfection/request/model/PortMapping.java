package com.reeman.robot.disinfection.request.model;

public class PortMapping {

    private int position;
    private int port;
    private int floor;

    public int getPosition() {
        return position;
    }

    public void setPosition(int position) {
        this.position = position;
    }

    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public int getFloor() {
        return floor;
    }

    public void setFloor(int floor) {
        this.floor = floor;
    }

    @Override
    public String toString() {
        return "PortMapping{" +
                "port=" + port +
                ", floor=" + floor +
                '}';
    }

    public PortMapping(int port, int floor) {
        this.port = port;
        this.floor = floor;
    }

    public PortMapping(int position, int port, int floor) {
        this.position = position;
        this.port = port;
        this.floor = floor;
    }

    public PortMapping() {
    }
}
