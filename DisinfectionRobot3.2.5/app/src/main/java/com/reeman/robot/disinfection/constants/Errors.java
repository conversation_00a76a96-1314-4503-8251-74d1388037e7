package com.reeman.robot.disinfection.constants;

import android.content.Context;


import com.reeman.robot.disinfection.R;

import java.util.HashMap;
import java.util.Map;

public class Errors {
    private static Map<Integer, Integer> errors;

    static {
        errors = new HashMap<>();
        errors.put(2, R.string.exception_remote_cancel_navigation);
        errors.put(4, R.string.exception_encounter_obstacle_in_the_way);
        errors.put(5, R.string.exception_encounter_obstacle_in_target_point);
        errors.put(6, R.string.exception_not_in_work_space);
        errors.put(7, R.string.exception_forbidden_zone);
        errors.put(-1, R.string.exception_tripped_in_obstacle);
    }

    public static String getErrorByCode(Context context, int code) {
        return context.getString(errors.get(code));
    }

    public static String getHardwareError(Context context, String sensor) {
        char laser = sensor.charAt(0);
        char mileMeter = sensor.charAt(1);
        char domain = sensor.charAt(2);
        char imu = sensor.charAt(3);
        char overCurrent = sensor.charAt(4);
        char visual = sensor.charAt(5);
        if (laser != '0') {
            return context.getString(R.string.exception_laser_error);
        }
        if (mileMeter != '0') {
            return context.getString(R.string.exception_speedometer_error);
        }
        if (domain != '0') {
            return context.getString(R.string.exception_chassis_failure);
        }
        if (imu != '0') {
            return context.getString(R.string.exception_imu_error);
        }
        if (overCurrent != '0') {
            return context.getString(R.string.exception_chassis_over_current);
        }
        if (visual != '0') {
            return context.getString(R.string.exception_visual_mark);
        }
        return "";
    }

    public static int getFaultReason(String sensor) {
        char laser = sensor.charAt(0);
        char mileMeter = sensor.charAt(1);
        char domain = sensor.charAt(2);
        char imu = sensor.charAt(3);
        char overCurrent = sensor.charAt(4);
        char visual = sensor.charAt(5);
        if (laser != '0') {
            return 1;
        }
        if (mileMeter != '0') {
            return 2;
        }
        if (domain != '0') {
            return 3;
        }
        if (imu != '0') {
            return 4;
        }
        if (overCurrent != '0') {
            return 5;
        }
        if (visual != '0') {
            return 6;
        }
        return 0;
    }
}
