package com.reeman.robot.disinfection.activities;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.SharedPreferences;
import android.graphics.Rect;
import android.media.AudioManager;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewTreeObserver;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.ListPopupWindow;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.ScrollView;
import android.widget.SeekBar;
import android.widget.Spinner;
import android.widget.TextView;

import androidx.core.content.res.ResourcesCompat;

import com.reeman.robot.disinfection.R;
import com.reeman.robot.disinfection.base.BaseActivity;
import com.reeman.robot.disinfection.constants.Constant;
import com.reeman.robot.disinfection.contract.SettingContract;
import com.reeman.robot.disinfection.event.Event;
import com.reeman.robot.disinfection.presenter.impl.SettingPresenter;
import com.reeman.robot.disinfection.utils.LocaleUtil;
import com.reeman.robot.disinfection.utils.PackageUtils;
import com.reeman.robot.disinfection.utils.ScreenUtils;
import com.reeman.robot.disinfection.utils.SoftKeyboardStateWatcher;
import com.reeman.robot.disinfection.utils.SpManager;
import com.reeman.robot.disinfection.utils.ToastUtils;
import com.reeman.robot.disinfection.widgets.BindRobotPopupWindow;
import com.reeman.robot.disinfection.widgets.ChooseItemPopupWindow;
import com.reeman.robot.disinfection.widgets.SetLockScreenPopupWindow;
import com.tencent.bugly.beta.Beta;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;

import static com.reeman.robot.disinfection.base.BaseApplication.controller;


public class SettingActivity extends BaseActivity implements View.OnClickListener,
        SettingContract.View,
        SetLockScreenPopupWindow.OnEventListener,
        ChooseItemPopupWindow.OnItemChosenListener, SoftKeyboardStateWatcher.SoftKeyboardStateListener {

    private RadioGroup rgLockScreen, rgVoiceBroadcast, rgGatingSwitch, rgElevatorSwitch;
    private SeekBar sbLowPower;
    private SeekBar sbSysVolume;
    private EditText etDelayTime;
    private TextView tvLowPower;
    private TextView tvSysVolume;
    private TextView tvSpeed;
    private TextView tvSetting;
    private SettingPresenter presenter;
    private int streamMaxVolume;
    private TextView tvUserName;
    private TextView tvLogout;
    private TextView tvCurrentLanguage;
    private SoftKeyboardStateWatcher softKeyboardStateWatcher;
    private EditText etDisinfectionPrompt;
    private Button btnSave;
    private Button btnTryListen;
    private ScrollView scrollView;
    private TextView tvBindRobot;
    private BindRobotPopupWindow bindRobotPopupWindow;
    private Spinner spGatingSerialPort, spElevatorSerialPort;
    private LinearLayout llGatingPort, llElevatorPort;
    private Button btnElevatorSetting;

    @Override
    protected boolean shouldResponse2TimeEvent() {
        return true;
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        if (hasFocus) {
            ScreenUtils.hideBottomUIMenu(this);
        }
    }


    @Override
    protected int getLayoutRes() {
        return R.layout.activity_setting;
    }

    @Override
    protected void initView() {
        LinearLayout llRoot = $(R.id.ll_root);
        getWindow().getDecorView().getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                Rect rect = new Rect();
                llRoot.getWindowVisibleDisplayFrame(rect);
                int rootInvisibleHeight = llRoot.getRootView().getHeight() - rect.bottom;
                if (rootInvisibleHeight > 100) {
                    int[] location = new int[2];
                    View currentFocus = getCurrentFocus();
                    if (currentFocus == null) return;
                    currentFocus.getLocationInWindow(location);
                    int scrollHeight = (location[1] + currentFocus.getHeight()) - rect.bottom;
                    llRoot.scrollTo(0, scrollHeight + 20);
                } else {
                    ScreenUtils.hideBottomUIMenu(SettingActivity.this);
                    llRoot.scrollTo(0, 0);
                }
            }
        });

        scrollView = $(R.id.sv_content);
        tvSetting = $(R.id.tv_sys_setting);

        TextView tvBack = $(R.id.tv_back);
        tvBack.setOnClickListener(this);

        tvLogout = $(R.id.tv_logout);
        tvLogout.setOnClickListener(this);

        tvUserName = $(R.id.tv_current_username);

        tvCurrentLanguage = $(R.id.tv_current_language);

        Button btnSwitchLanguage = $(R.id.btn_switch_language);
        btnSwitchLanguage.setOnClickListener(this);

        tvSpeed = $(R.id.tv_speed);
        tvSpeed.setOnClickListener(this);

        tvBindRobot = $(R.id.tv_bind_robot);
        tvBindRobot.setOnClickListener(this);

        rgLockScreen = $(R.id.rg_lock_screen);
        RadioButton rbOpenLockScreen = $(R.id.rb_open_lock_screen);
        rbOpenLockScreen.setOnClickListener(this);
        RadioButton rbCloseLockScreen = $(R.id.rb_close_lock_screen);
        rbCloseLockScreen.setOnClickListener(this);

        rgVoiceBroadcast = $(R.id.rg_voice_broadcast);

        sbLowPower = $(R.id.sb_low_power);
        sbLowPower.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (fromUser) {
                    tvLowPower.setText(progress + "");
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                int progress = seekBar.getProgress();
                if (progress < 10) {
                    sbLowPower.setProgress(10);
                    tvLowPower.setText(10 + "");
                } else if (progress > 80) {
                    sbLowPower.setProgress(80);
                    tvLowPower.setText(80 + "");
                }
            }
        });
        tvLowPower = $(R.id.tv_low_power);
        ImageButton ibRaisePower = $(R.id.ib_raise_power);
        ibRaisePower.setOnClickListener(this);
        ImageButton ibDecreasePower = $(R.id.ib_decrease_power);
        ibDecreasePower.setOnClickListener(this);

        sbSysVolume = $(R.id.sb_system_volume);
        sbSysVolume.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (fromUser) {
                    tvSysVolume.setText(progress + "");
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
            }
        });
        tvSysVolume = $(R.id.tv_sys_volume);
        ImageButton ibRaiseVolume = $(R.id.ib_raise_volume);
        ibRaiseVolume.setOnClickListener(this);
        ImageButton ibDecreaseVolume = $(R.id.ib_decrease_volume);
        ibDecreaseVolume.setOnClickListener(this);

        etDelayTime = $(R.id.et_delay_time);

        TextView tvCurrentVersion = $(R.id.tv_current_version);
        tvCurrentVersion.setOnClickListener(this);
        tvCurrentVersion.setText(PackageUtils.getVersion(this));

        etDisinfectionPrompt = $(R.id.et_disinfection_prompt);
        btnTryListen = $(R.id.btn_try_synthesize);
        btnTryListen.setOnClickListener(this);
        btnSave = $(R.id.btn_save);
        btnSave.setOnClickListener(this);
        btnSave.setEnabled(false);
        llGatingPort = $(R.id.ll_gating_port);
        llElevatorPort = $(R.id.ll_elevator_port);
        rgGatingSwitch = $(R.id.rg_gating_switch);
        rgElevatorSwitch = $(R.id.rg_elevator_switch);
        btnElevatorSetting = $(R.id.btn_elevator_setting);
        btnElevatorSetting.setOnClickListener(this);
        $(R.id.rb_gating_open).setOnClickListener(this);
        $(R.id.rb_gating_close).setOnClickListener(this);
        $(R.id.rb_elevator_open).setOnClickListener(this);
        $(R.id.rb_elevator_close).setOnClickListener(this);
        $(R.id.btn_upload).setOnClickListener(this);
        initSerialPort();
    }

    private void initSerialPort() {
        spGatingSerialPort = $(R.id.sp_gating_serial_port);
        spElevatorSerialPort = $(R.id.sp_elevator_serial_port);
        File file = new File("/dev");
        File[] files = file.listFiles();

        List<String> list = new ArrayList<>();
        if (files == null || files.length == 0) {
            list.add(getString(R.string.text_can_not_find_serial_port));
        } else {
            for (File file1 : files) {
                if (file1.getName().startsWith("tty"))
                    list.add(file1.getAbsolutePath());
            }
        }
        spGatingSerialPort.setAdapter(new ArrayAdapter<>(this, R.layout.layout_spinner_item, list));
        String gating = SpManager.getInstance().getString(Constant.KEY_GATING_SERIAL_PORT, Constant.KEY_DEFAULT_GATING_SERIAL_PORT);
        int indexGating = list.indexOf(gating);
        if (indexGating != -1) {
            spGatingSerialPort.setSelection(indexGating);
        }
        spElevatorSerialPort.setAdapter(new ArrayAdapter<>(this, R.layout.layout_spinner_item, list));
        String elevator = SpManager.getInstance().getString(Constant.KEY_ELEVATOR_SERIAL_PORT, Constant.KEY_DEFAULT_ELEVATOR_SERIAL_PORT);
        int indexElevator = list.indexOf(elevator);
        if (indexElevator != -1) {
            spElevatorSerialPort.setSelection(indexElevator);
        }
    }

    @SuppressLint("SetTextI18n")
    @Override
    protected void initData() {

        softKeyboardStateWatcher = new SoftKeyboardStateWatcher(getWindow().getDecorView());
        softKeyboardStateWatcher.addSoftKeyboardStateListener(this);
        presenter = new SettingPresenter(this);

        SharedPreferences instance = SpManager.getInstance();
        boolean lockScreen = instance.getBoolean(Constant.LOCK_SCREEN, Constant.DEFAULT_LOCK_SCREEN);
        rgLockScreen.check(lockScreen ? R.id.rb_open_lock_screen : R.id.rb_close_lock_screen);

        boolean voiceBroadcast = instance.getBoolean(Constant.VOICE_BROADCAST, Constant.DEFAULT_VOICE_BROADCAST);
        rgVoiceBroadcast.check(voiceBroadcast ? R.id.rb_open_voice_broadcast : R.id.rb_close_voice_broadcast);

        boolean gatingSwitch = instance.getBoolean(Constant.KEY_GATING_SWITCH, Constant.DEFAULT_GATING_SWITCH);
        rgGatingSwitch.check(gatingSwitch ? R.id.rb_gating_open : R.id.rb_gating_close);
        llGatingPort.setVisibility(gatingSwitch ? View.VISIBLE : View.GONE);

        boolean elevatorSwitch = instance.getBoolean(Constant.KEY_ELEVATOR_SWITCH, Constant.DEFAULT_ELEVATOR_SWITCH);
        rgElevatorSwitch.check(elevatorSwitch ? R.id.rb_elevator_open : R.id.rb_elevator_close);
        btnElevatorSetting.setVisibility(elevatorSwitch ? View.VISIBLE : View.GONE);
        int lowPower = instance.getInt(Constant.LOW_POWER, Constant.DEFAULT_LOW_POWER);
        sbLowPower.setProgress(lowPower);
        tvLowPower.setText(lowPower + "");

        int sysVolume = instance.getInt(Constant.SYS_VOLUME, Constant.DEFAULT_MEDIA_VOLUME);
        AudioManager audioManager = (AudioManager) getSystemService(Context.AUDIO_SERVICE);
        streamMaxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC);
        sbSysVolume.setMax(streamMaxVolume);
        sbSysVolume.setProgress(sysVolume);
        tvSysVolume.setText(sysVolume + "");

        int delayTime = instance.getInt(Constant.DELAY_TIME, Constant.DEFAULT_DELAY_TIME);
        etDelayTime.setText(delayTime + "");

        String disinfectionPrompt = instance.getString(Constant.DISINFECTION_PROMPT, getString(R.string.voice_stay_away_from_me));
        if (etDisinfectionPrompt != null) {
            etDisinfectionPrompt.setText(disinfectionPrompt);
            etDisinfectionPrompt.setSelection(disinfectionPrompt.length());
        }
    }

    private void refreshLoginState() {
        String username = SpManager.getInstance().getString(Constant.USERNAME, "");
        if (!TextUtils.isEmpty(username)) {
            tvUserName.setText(username);
            tvLogout.setText(getString(R.string.text_exit_login));
        } else {
            tvUserName.setText(getString(R.string.text_not_login));
            tvLogout.setText(getString(R.string.text_login));
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        mHandler.postDelayed(() -> scrollView.fullScroll(View.FOCUS_UP), 300);
        refreshLoginState();
        refreshCurrentLanguage();
        controller.getNavSpeed();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        softKeyboardStateWatcher.removeSoftKeyboardStateListener(this);
    }

    private void refreshCurrentLanguage() {
        int[] nationalFlags = new int[]{R.drawable.english_national_flag, R.drawable.china_national_flag, R.drawable.japan_national_flag, R.drawable.korea_national_flag};
        int languageType = SpManager.getInstance().getInt(Constant.KEY_LANGUAGE_TYPE, Constant.DEFAULT_LANGUAGE_TYPE);
        String localeType = LocaleUtil.getLocaleType(languageType);
        tvCurrentLanguage.setText(localeType);
        tvCurrentLanguage.setCompoundDrawablePadding(10);
        tvCurrentLanguage.setCompoundDrawablesWithIntrinsicBounds(getResources().getDrawable(nationalFlags[languageType]), null, null, null);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.btn_switch_language:
                BaseActivity.start(this, LanguageChooseActivity.class, null);
                break;
            case R.id.rb_open_lock_screen:
                showLockScreenWindow(true);
                break;
            case R.id.rb_close_lock_screen:
                showLockScreenWindow(false);
                break;
            case R.id.tv_logout:
                presenter.onLogoutOrLogInClicked(this);
                break;
            case R.id.tv_back:
                presenter.persist(this,
                        rgGatingSwitch.getCheckedRadioButtonId(),
                        rgElevatorSwitch.getCheckedRadioButtonId(),
                        (String) spGatingSerialPort.getSelectedItem(),
                        (String) spElevatorSerialPort.getSelectedItem(),
                        rgVoiceBroadcast.getCheckedRadioButtonId(),
                        rgLockScreen.getCheckedRadioButtonId(),
                        tvSpeed.getText().toString(),
                        sbLowPower.getProgress(),
                        sbSysVolume.getProgress(),
                        etDelayTime.getText().toString()
                );
                break;
            case R.id.ib_raise_power:
                int maxPower = sbLowPower.getProgress() + 1;
                if (maxPower <= 80) {
                    tvLowPower.setText(maxPower + "");
                    sbLowPower.setProgress(maxPower);
                }
                break;
            case R.id.ib_decrease_power:
                int minPower = sbLowPower.getProgress() - 1;
                if (minPower >= 10) {
                    tvLowPower.setText(minPower + "");
                    sbLowPower.setProgress(minPower);
                }
                break;
            case R.id.ib_raise_volume:
                int maxVolume = sbSysVolume.getProgress() + 1;
                if (maxVolume <= streamMaxVolume) {
                    tvSysVolume.setText(maxVolume + "");
                    sbSysVolume.setProgress(maxVolume);
                }
                break;
            case R.id.ib_decrease_volume:
                int minVolume = sbSysVolume.getProgress() - 1;
                if (minVolume >= 0) {
                    tvSysVolume.setText(minVolume + "");
                    sbSysVolume.setProgress(minVolume);
                }
                break;
            case R.id.tv_current_version:
                Beta.checkUpgrade();
                break;
            case R.id.tv_speed:
                showSpeedChoicePopupWindow();
                break;
            case R.id.tv_bind_robot:
                showBindRobotWindow();
                break;
            case R.id.btn_save:
                presenter.onSaveDisinfectionPromptAudio(this, etDisinfectionPrompt.getText());
                break;
            case R.id.btn_try_synthesize:
                presenter.onTryListen(this, etDisinfectionPrompt.getText());
                break;
            case R.id.rb_gating_open:
            case R.id.rb_gating_close:
                llGatingPort.setVisibility(v.getId() == R.id.rb_gating_open ? View.VISIBLE : View.GONE);
                break;
            case R.id.rb_elevator_open:
            case R.id.rb_elevator_close:
//                llElevatorPort.setVisibility(v.getId() == R.id.rb_elevator_open ? View.VISIBLE : View.GONE);
                btnElevatorSetting.setVisibility(v.getId() == R.id.rb_elevator_open ? View.VISIBLE : View.GONE);
                break;
            case R.id.btn_elevator_setting:
                start(this, ElevatorSettingActivity.class);
                break;
            case R.id.btn_upload:
                uploadLogs();
                break;
        }
    }

    private void showBindRobotWindow() {
        if (bindRobotPopupWindow == null) {
            bindRobotPopupWindow = new BindRobotPopupWindow(this);
        }
        bindRobotPopupWindow.showAtLocation(getWindow().getDecorView(), Gravity.BOTTOM, 0, 0);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onSpeedObtained(Event.OnSpeedEvent event) {
        String speed = String.format(Locale.getDefault(), "%.1f", event.speed);
        tvSpeed.setText(speed + "m/s");
    }

    /**
     * 显示速度旋转
     */
    private void showSpeedChoicePopupWindow() {
        ChooseItemPopupWindow listPopupWindow = new ChooseItemPopupWindow(this,
                tvSetting,
                getString(R.string.text_choose_speed),
                Arrays.asList(getResources().getStringArray(R.array.speed)));
        listPopupWindow.setOnItemChosenListener(this);
        listPopupWindow.show();
    }

    @Override
    public void onBackPressed() {
        onWindowFocusChanged(true);
    }

    @Override
    public void showInvalidLowPower() {
        ToastUtils.showShortToast(getString(R.string.text_invalid_low_power));
    }

    @Override
    public void showInvalidDelayTime() {
        ToastUtils.showShortToast(getString(R.string.text_invalid_delay_time));
    }

    @Override
    public void onLogoutSuccess() {
        ToastUtils.showShortToast(getString(R.string.text_logout_success));
        refreshLoginState();
    }

    @Override
    public void onSaveSuccess() {
        finish();
    }

    private void showLockScreenWindow(boolean isOpenLock) {
        SetLockScreenPopupWindow lockScreenPopupWindow = new SetLockScreenPopupWindow(this, isOpenLock);
        lockScreenPopupWindow.showAtLocation(getWindow().getDecorView(), Gravity.BOTTOM, 0, 0);
        lockScreenPopupWindow.setOnEventListener(this);
    }

    /**
     * 用户确认锁屏密码
     *
     * @param window
     * @param editText
     * @param password
     * @param isOpenLock
     */
    @Override
    public void onConfirm(SetLockScreenPopupWindow window, EditText editText, String password, boolean isOpenLock) {
        presenter.onLockScreenConfirmed(window, editText, password, isOpenLock);
    }

    /**
     * 锁屏密码不合法
     */
    @Override
    public void onLockScreenInvalid() {
        ToastUtils.showShortToast(this.getString(R.string.text_invalid_lock_screen_password));
    }

    /**
     * 第一次确认密码
     *
     * @param editText
     */
    @Override
    public void onLockScreenFirstPasswordConfirmed(EditText editText) {
        editText.setText("");
        editText.setHint(this.getString(R.string.text_enter_lock_screen_password_again));
    }

    /**
     * 关闭锁屏时密码错误
     *
     * @param editText
     */
    @Override
    public void onCloseLockScreenPasswordError(EditText editText) {
        editText.setText("");
        ToastUtils.showShortToast(getString(R.string.text_password_error));
    }

    /**
     * 打开锁屏成功
     *
     * @param window
     */
    @Override
    public void onOpenLockScreenSuccess(SetLockScreenPopupWindow window) {
        window.dismiss();
    }

    /**
     * 两次密码不一致
     *
     * @param editText
     */
    @Override
    public void onLockScreenPasswordNotSame(EditText editText) {
        editText.setText("");
        ToastUtils.showShortToast(getString(R.string.text_passwords_are_not_same));
    }

    /**
     * 关闭锁屏成功
     *
     * @param window
     */
    @Override
    public void onCloseLockScreenSuccess(SetLockScreenPopupWindow window) {
        window.dismiss();
    }

    /**
     * 锁屏密码窗口被取消（手动取消或者设置成功之后代码关闭)
     */
    @Override
    public void onDismiss() {
        presenter.onDismissOperation();
    }

    @Override
    public void onLockScreenDismiss() {
        //刷新当前锁屏状态
        rgLockScreen.check(SpManager.getInstance().getBoolean(Constant.LOCK_SCREEN, false) ? R.id.rb_open_lock_screen : R.id.rb_close_lock_screen);
    }

    @Override
    public void onSaveAudioSuccess() {
        ToastUtils.showShortToast(getString(R.string.text_save_success));
        btnSave.setEnabled(false);
        btnSave.setBackground(ResourcesCompat.getDrawable(getResources(), R.drawable.bg_common_button_inactive, getTheme()));
    }

    @Override
    public void onSynthesizeStart() {
        btnTryListen.setEnabled(false);
        btnTryListen.setBackground(ResourcesCompat.getDrawable(getResources(), R.drawable.bg_common_button_inactive, getTheme()));
        btnSave.setEnabled(false);
        btnSave.setBackground(ResourcesCompat.getDrawable(getResources(), R.drawable.bg_common_button_inactive, getTheme()));
    }

    @Override
    public void onSynthesizeEnd() {
        btnTryListen.setEnabled(true);
        btnTryListen.setBackgroundResource(R.drawable.selector_common_button);
        btnSave.setEnabled(true);
        btnSave.setBackground(ResourcesCompat.getDrawable(getResources(), R.drawable.selector_common_button, getTheme()));
    }

    @Override
    public void onSynthesizeError(String message) {
        btnTryListen.setEnabled(true);
        btnTryListen.setBackgroundResource(R.drawable.selector_common_button);
        ToastUtils.showShortToast(getString(R.string.text_synthesize_error, message));
    }

    @Override
    public void showSerialPortError(String msg) {
        ToastUtils.showShortToast(msg);
    }

    /**
     * 速度选择成功
     *
     * @param window
     * @param speed
     */
    @Override
    public void onSpeedChosen(ListPopupWindow window, String speed) {
        tvSpeed.setText(speed + "m/s");
        window.dismiss();
    }

    @Override
    public void onSoftKeyboardOpened(int keyboardHeightInPx) {

    }


    @Override
    public void onSoftKeyboardClosed() {
        etDelayTime.clearFocus();
        tvUserName.requestFocus();
        ScreenUtils.hideBottomUIMenu(this);
    }
}