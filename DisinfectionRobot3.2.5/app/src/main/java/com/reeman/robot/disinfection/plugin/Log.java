package com.reeman.robot.disinfection.plugin;

import android.os.Environment;

import com.elvishew.xlog.LogConfiguration;
import com.elvishew.xlog.LogLevel;
import com.elvishew.xlog.XLog;
import com.elvishew.xlog.flattener.ClassicFlattener;
import com.elvishew.xlog.printer.AndroidPrinter;
import com.elvishew.xlog.printer.Printer;
import com.elvishew.xlog.printer.file.FilePrinter;
import com.elvishew.xlog.printer.file.backup.NeverBackupStrategy;
import com.elvishew.xlog.printer.file.clean.FileLastModifiedCleanStrategy;
import com.elvishew.xlog.printer.file.naming.DateFileNameGenerator;
import com.reeman.robot.disinfection.BuildConfig;

public class Log {

    public static void init() {
        LogConfiguration config = new LogConfiguration.Builder()
                .logLevel(BuildConfig.DEBUG ? LogLevel.ALL : LogLevel.WARN)
                .tag(BuildConfig.TAG)
                .disableThreadInfo()
                .disableStackTrace()
                .build();

        Printer androidPrinter = new AndroidPrinter(true);
        Printer filePrinter = new FilePrinter
                .Builder(Environment.getExternalStorageDirectory() + "/disinfection_robot")
                .fileNameGenerator(new DateFileNameGenerator())
                .backupStrategy(new NeverBackupStrategy())
                .flattener(new ClassicFlattener())
                .cleanStrategy(new FileLastModifiedCleanStrategy(3 * 24 * 60 * 60 * 1000))
                .build();

        XLog.init(config, androidPrinter, filePrinter);
    }

}
