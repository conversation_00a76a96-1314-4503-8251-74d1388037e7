package com.reeman.robot.disinfection.request.model;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class Room {

    @SerializedName("n")
    public String name;
    @SerializedName("c")
    public List<Double> coordination;

    @Override
    public String toString() {
        return "Room{" +
                "name='" + name + '\'' +
                ", coordination=" + coordination +
                '}';
    }
}
