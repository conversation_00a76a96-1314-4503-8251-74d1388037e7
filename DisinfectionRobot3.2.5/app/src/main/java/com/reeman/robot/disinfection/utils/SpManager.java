package com.reeman.robot.disinfection.utils;

import android.content.Context;
import android.content.SharedPreferences;

import com.reeman.robot.disinfection.constants.Constant;

import java.util.HashSet;
import java.util.Set;

/**
 * @ClassName: SpManager.java
 * @Author: <PERSON><PERSON><PERSON><PERSON>(<EMAIL>)
 * @Date: 2022/1/9 15:03
 * @Description: sp存储工具类
 */
public class SpManager {
    private static SharedPreferences sSharedPreferences;

    public static void init(Context context) {
        sSharedPreferences = context.getSharedPreferences(Constant.SP_NAME, Context.MODE_PRIVATE);
    }

    public static SharedPreferences getInstance() {
        return sSharedPreferences;
    }

    public static String decodeString(String key){
        return sSharedPreferences.getString(key, "");
    }

    public static String decodeString(String key,String defaultValue){
        return sSharedPreferences.getString(key, defaultValue);
    }

    public static void encode(String key,String value){
        sSharedPreferences.edit().putString(key, value).apply();
    }

    public static Boolean decodeBool(String key){
        return sSharedPreferences.getBoolean(key, false);
    }

    public static Boolean decodeBool(String key,boolean defaultValue){
        return sSharedPreferences.getBoolean(key, defaultValue);
    }

    public static void encode(String key,boolean value){
        sSharedPreferences.edit().putBoolean(key, value).apply();
    }

    public static Integer decodeInt(String key){
        return sSharedPreferences.getInt(key, 0);
    }

    public static Integer decodeInt(String key,int defaultValue){
        return sSharedPreferences.getInt(key, defaultValue);
    }

    public static void encode(String key,int value){
        sSharedPreferences.edit().putInt(key, value).apply();
    }

    public static Long decodeLong(String key){
        return sSharedPreferences.getLong(key, 0L);
    }

    public static Long decodeLong(String key,long defaultValue){
        return sSharedPreferences.getLong(key, defaultValue);
    }

    public static void encode(String key,long value){
        sSharedPreferences.edit().putLong(key, value).apply();
    }

    public static Float decodeFloat(String key){
        return sSharedPreferences.getFloat(key, 0.0F);
    }

    public static Float decodeFloat(String key,float defaultValue){
        return sSharedPreferences.getFloat(key, defaultValue);
    }

    public static void encode(String key,float value){
        sSharedPreferences.edit().putFloat(key, value).apply();
    }

    public static Set<String> decodeStringSet(String key){
        return sSharedPreferences.getStringSet(key, null);
    }

    public static Set<String> decodeStringSet(String key,Set<String> defaultValue){
        return sSharedPreferences.getStringSet(key, defaultValue);
    }

    public static void encode(String key, HashSet<String> value){
        sSharedPreferences.edit().putStringSet(key, value).apply();
    }

}
