package com.reeman.robot.disinfection.contract;

import android.content.Context;

import com.reeman.robot.disinfection.presenter.IPresenter;
import com.reeman.robot.disinfection.repository.entities.Task;
import com.reeman.robot.disinfection.view.IView;

import java.util.Date;

public interface TaskExecutingContract {

    interface Presenter extends IPresenter {

        void startTask(Context context, Task task, Date startTime, int startPower);

        String getNextDestination();

        void onNavRes(Context context, String result);

        void onPointNotFound(Context context);

        void onPointFound();

        void onStartCharge();

        void onEmergencyStopStateChange(Context context, int emergencyStopState);

        void onBtnFinishTaskClicked(Context context);

        void onBtnPauseOrResumeClicked(Context context);

        void onChargingPileNotFound(Context context);

        void onLowPower(Context context);

        void onMapCurrent(String map);

        void onPositionLoaded(String[] position);

        void onApplyMapSuccess(String map);

        void onMarkPoint(Context context);

        void onPause();

    }

    interface View extends IView {

        void updateTaskProgress(int taskMode, long taskDuration, long waste);

        void showDisinfectionSwitchTurnOnView();

        void showDisinfectionSwitchTurnOffView();

        void showTaskFinishedView();

        void showTaskStateView(int currentTaskState);

        void showReturningJourneyView(String string);

        void showTaskCanceled();

        void showLockScreenView(Runnable runnable);

        void showLockScreenTimeOutView(Runnable timeoutRunnable, Runnable successRunnable);

        void showSensorErrorView(String hardwareError);

        void showChargingPileNotFound();

        void showCanNotReachChargingPileView(String error);

        void showApplyMap();

        void showFailedView(int code, String voice, String text);

        void showOpenDoorSuccessView();

        void showCloseDoorSuccessView();

        void showTakeTheElevatorNaviPoint(String point);

        void showTaskEndByElevatorError(String msg);

        void showOnPortOpenFailed(Exception e);

        void onPermissionRequestFailed();

        void onShowElevatorDialog(String msg);

        void onElevatorDialogDismiss();
    }
}
