package com.reeman.robot.disinfection.presenter.impl;

import android.app.Dialog;
import android.content.Context;
import android.content.SharedPreferences;
import android.media.MediaPlayer;
import android.net.wifi.WifiManager;
import android.os.CountDownTimer;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.util.Log;

import androidx.annotation.NonNull;

import com.elvishew.xlog.XLog;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.reeman.robot.disinfection.R;
import com.reeman.robot.disinfection.constants.Constant;
import com.reeman.robot.disinfection.constants.Errors;
import com.reeman.robot.disinfection.contract.TaskExecutingContract;
import com.reeman.robot.disinfection.controller.AccessController;
import com.reeman.robot.disinfection.elevatorcontrol.ElevatorManager;
import com.reeman.robot.disinfection.elevatorcontrol.ElevatorState;
import com.reeman.robot.disinfection.event.Event;
import com.reeman.robot.disinfection.repository.entities.Task;
import com.reeman.robot.disinfection.request.factory.ServiceFactory;
import com.reeman.robot.disinfection.request.model.PortMapping;
import com.reeman.robot.disinfection.request.model.Room;
import com.reeman.robot.disinfection.request.model.TaskRecord;
import com.reeman.robot.disinfection.utils.LocaleUtil;
import com.reeman.robot.disinfection.utils.SpManager;
import com.reeman.robot.disinfection.utils.VoiceHelper;
import com.reeman.robot.disinfection.widgets.EasyDialog;
import com.reeman.robot.disinfection.widgets.SimpleCountDownTimer;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.StringTokenizer;

import io.reactivex.rxjava3.schedulers.Schedulers;

import static com.reeman.robot.disinfection.base.BaseApplication.controller;
import static com.reeman.robot.disinfection.base.BaseApplication.mApp;

public class TaskExecutingPresenter implements TaskExecutingContract.Presenter, AccessController.OnAccessControlListener, ElevatorManager.ElevatorStateCallback {
    private ElevatorManager elevatorManager;

    private final TaskExecutingContract.View view;
    //当前消毒点
    private int currentDisinfectionPoint = 0;

    //任务计时定时器
    private CountDownTimer durationCountDownTimer;

    //停留时间计时器
    private CountDownTimer stayCountDownTimer;

    //找不到目标点计数
    private int canNotFindPointCount = 0;

    //第一个找到的目标点
    private int firstFoundPoint = -1;

    //当前任务
    private Task task;

    //当前任务所处阶段
    private int currentTaskState = TASK_STATE_RUNNING;

    public static final int TASK_STATE_RUNNING = 0; //运行中
    public static final int TASK_STATE_PAUSE = 1;   //暂停中
    public static final int TASK_STATE_RETURNING = 2; //返程中
    public static final int TASK_STATE_ELEVATOR = 3;

    //剩余停留时长
    private long stayTimeMillsUntilFinished = 0;

    //剩余时长循环
    private long durationTimeMillsUtilFinished = 0;

    //开始时间
    private Date startTime;

    //开始电量
    private int startPower;

    //失败点位
    private final Set<String> failedPoints = new HashSet<>();

    private int failedCount = 0;
    private long lastClickTimeMills;

    private boolean gatingSwitch;

    private boolean elevatorSwitch;

    private Runnable poseRunnable;
    private final Handler mHandler = new Handler(Looper.getMainLooper());

    //当前楼层
    private int nowMap;
    //目标楼层
    private int targetMap;

    private String targetPoint;
    //任务完成
    private boolean taskFinish = false;

    private List<String> disinfectionFloors;

    public TaskExecutingPresenter(TaskExecutingContract.View view) {
        this.view = view;
    }

    @Override
    public String getNextDestination() {
        return currentDisinfectionPoint + "";
    }

    public int getCurrentDisinfectionPoint() {
        return currentDisinfectionPoint;
    }


    private void gotoTargetPoint(String point) {
        view.showTakeTheElevatorNaviPoint(point);
        targetPoint = point;
        controller.navigationByPoint(point);
    }

    private void gotoTargetFloor() {
        if (disinfectionFloors.size() == 0) {
            String defaultMap = SpManager.getInstance().getString(Constant.DEFAULT_MAP, "");
            XLog.e("任务完成 :" + nowMap + " : " + defaultMap);
            if (nowMap == Integer.parseInt(defaultMap)) {
                onTaskFinished();
            } else {
                if (task.switchMode == 0) {
                    controller.stopDisinfection();
                    view.showDisinfectionSwitchTurnOffView();
                }
                taskFinish = true;
                targetMap = Integer.parseInt(defaultMap);
                ElevatorState.goalFloor = targetMap;
                currentTaskState = TASK_STATE_ELEVATOR;
                gotoTargetPoint(mApp.getString(R.string.elevator_out_point));
            }
        } else {
            String floor = disinfectionFloors.get(0);
            XLog.e("去下一楼层 :" + nowMap + " : " + floor);
            if (floor.equals(nowMap + "")) {
                //全程打开消毒则任务开始时打开消毒开关
                if (task.switchMode == 0) {
                    controller.startDisinfection();
                    view.showDisinfectionSwitchTurnOnView();
                }
                gotoTargetPoint(calcNextTargetPoint());
                disinfectionFloors.remove(floor);
            } else {
                if (task.switchMode == 0) {
                    controller.stopDisinfection();
                    view.showDisinfectionSwitchTurnOffView();
                }
                targetMap = Integer.parseInt(floor);
                ElevatorState.goalFloor = targetMap;
                currentTaskState = TASK_STATE_ELEVATOR;
                gotoTargetPoint(mApp.getString(R.string.elevator_out_point));
            }
        }
    }

    public String calcNextTargetPoint() {
        ++currentDisinfectionPoint;
        return currentDisinfectionPoint + "";
    }


    @Override
    public void startTask(Context context, Task task, Date startTime, int startPower) {
        this.task = task;
        this.startTime = startTime;
        this.startPower = startPower;
        this.nowMap = Integer.parseInt(Event.getMapEvent().map);
        SharedPreferences sp = SpManager.getInstance();
        gatingSwitch = sp.getBoolean(Constant.KEY_GATING_SWITCH, Constant.DEFAULT_GATING_SWITCH);
        elevatorSwitch = sp.getBoolean(Constant.KEY_ELEVATOR_SWITCH, Constant.DEFAULT_ELEVATOR_SWITCH);
//        if (gatingSwitch) {
//            //开启门控消毒模式
//            try {
//                String serialPort = sp.getString(Constant.KEY_GATING_SERIAL_PORT, Constant.KEY_DEFAULT_GATING_SERIAL_PORT);
//                AccessController.getInstance().init(this, serialPort);
//            } catch (Exception e) {
//                XLog.e("门控串口打开失败:" + e.getMessage());
//                finishTask(-1, "", context.getString(R.string.text_gating_port_open_failed));
//                return;
//            }
//            poseRunnable = new Runnable() {
//                @Override
//                public void run() {
//                    controller.getCurrentPosition();
//                    mHandler.postDelayed(this, 1000);
//                }
//            };
//            mHandler.postDelayed(poseRunnable, 2000);
//        }
        XLog.w("开始任务：" + task.toString());
        //开始任务计时
        long taskDuration = task.taskMode == 0 ? Integer.MAX_VALUE : task.durationTime * 1000;
        startDurationCountdownTimer(context, taskDuration);

        //全程打开消毒则任务开始时打开消毒开关
        if (task.switchMode == 0) {
            controller.startDisinfection();
            view.showDisinfectionSwitchTurnOnView();
        }
        if (elevatorSwitch) {
            if (task.disinfectionFloors != null) {
                disinfectionFloors = new ArrayList<>();
                StringTokenizer stringTokenizer = new StringTokenizer(task.disinfectionFloors, "\n");
                while (stringTokenizer.hasMoreElements()) {
                    disinfectionFloors.add(stringTokenizer.nextToken());
                }
            }
            elevatorManager = ElevatorManager.getInstance();
            elevatorManager.openPort(this);
            gotoTargetFloor();
        } else {
            gotoTargetPoint(calcNextTargetPoint());
        }
    }

    private void finishTask(int code, String voice, String text) {
        controller.stopDisinfection();
        controller.cancelCharge();
        controller.cancelNavigation();
        if (EasyDialog.isShow()) EasyDialog.getInstance().dismiss();
        view.showFailedView(code, voice, text);
    }

    private void startDurationCountdownTimer(Context context, long taskDuration) {
        durationCountDownTimer = new SimpleCountDownTimer(taskDuration, 1000) {
            private int timeTick = 0;

            @Override
            public void onFinish() {
                onGlobalTimerFinished(context);
            }

            @Override
            public void onTick(long millisUntilFinished) {
                durationTimeMillsUtilFinished = millisUntilFinished;
                view.updateTaskProgress(task.taskMode, task.durationTime, task.taskMode == 1 ? (task.durationTime * 1000 - millisUntilFinished) / 1000 : (Integer.MAX_VALUE - millisUntilFinished) / 1000);
                //返程中，不播报
                if (currentTaskState >= TASK_STATE_RETURNING) return;
                //播放中，不播报
                if (VoiceHelper.isPlaying()) return;

                if (++timeTick >= 8) {
                    timeTick = 0;
                    //关闭语音播报，不播报
                    if (!SpManager.getInstance().getBoolean(Constant.VOICE_BROADCAST, Constant.DEFAULT_VOICE_BROADCAST))
                        return;
                    //播放指定音频文件
                    int localeType = SpManager.getInstance().getInt(Constant.KEY_LANGUAGE_TYPE, Constant.DEFAULT_LANGUAGE_TYPE);
                    File file = new File(context.getFilesDir() + "/disinfection_voice/voice_disinfection_prompt_" + LocaleUtil.getLanguage(localeType) + ".wav");
                    if (file.exists()) {
                        VoiceHelper.playFile(file.getAbsolutePath());
                        return;
                    }
                    //播放默认音频文件
                    VoiceHelper.play("voice_stay_away_from_me");
                }
            }
        };
        durationCountDownTimer.start();
    }

    @Override
    public void onBtnFinishTaskClicked(Context context) {
        boolean lockScreen = SpManager.getInstance().getBoolean(Constant.LOCK_SCREEN, Constant.DEFAULT_LOCK_SCREEN);
        if (lockScreen) {
            onPauseTask();
            view.showLockScreenTimeOutView(() -> {
                XLog.w("密码输入超时，结束失败，继续任务");
                onResumeTask(context);
            }, this::onCancelTask);
            return;
        }
        onCancelTask();
    }

    private void onCancelTask() {
        XLog.w("点击结束按钮，取消任务");
        VoiceHelper.play("voice_task_finished");
        controller.cancelNavigation();
        controller.stopDisinfection();

        //上传消毒日志
        uploadTaskRecord(TaskRecord.create(task.taskName, task.taskMode, startTime.getTime(), 1, startPower, failedPoints.toString(), ""));
        if (controller.isChargingDocking()) controller.cancelCharge();
        if (durationCountDownTimer != null) durationCountDownTimer.cancel();
        if (stayCountDownTimer != null) stayCountDownTimer.cancel();
        view.showTaskFinishedView();
    }

    @Override
    public void onBtnPauseOrResumeClicked(Context context) {
        if (System.currentTimeMillis() - lastClickTimeMills < 300) return;
        lastClickTimeMills = System.currentTimeMillis();
        boolean lockScreen = SpManager.getInstance().getBoolean(Constant.LOCK_SCREEN, Constant.DEFAULT_LOCK_SCREEN);
        if (currentTaskState == TASK_STATE_RUNNING) {
            onPauseTask();
            XLog.w("暂停任务");
            if (lockScreen) {
                view.showLockScreenTimeOutView(() -> {
                    XLog.w("输入密码超时，继续任务");
                    onResumeTask(context);
                }, () -> {
                });
            }
        } else if (currentTaskState == TASK_STATE_PAUSE) {
            if (lockScreen) {
                view.showLockScreenView(() -> onResumeTask(context));
                return;
            }
            onResumeTask(context);
        }
    }

    /**
     * 当恢复任务
     */
    private void onResumeTask(Context context) {
        XLog.w("恢复任务");
        currentTaskState = TASK_STATE_RUNNING;
        view.showTaskStateView(currentTaskState);
        //剩余时间较短
        if (durationTimeMillsUtilFinished < 500) {
            onTaskFinished();
            return;
        }

        //剩余时间较长，启动任务计时器
        startDurationCountdownTimer(context, durationTimeMillsUtilFinished);

        //剩余停留时间较长，则重启停留定时器
        if (stayTimeMillsUntilFinished >= 500) {

            if (task.switchMode == 1 || task.switchMode == 0) {
                controller.startDisinfection();
                view.showDisinfectionSwitchTurnOnView();
            }

            startStayCountDownTimer(stayTimeMillsUntilFinished);
            return;
        }

        //中途被暂停，如果全程打开消毒则打开消毒，继续前往下一个目标点
        if (task.switchMode == 0) {
            controller.startDisinfection();
            view.showDisinfectionSwitchTurnOnView();
        }
        gotoTargetPoint(calcNextTargetPoint());
    }

    /**
     * 当暂停任务
     */
    private void onPauseTask() {
        currentTaskState = TASK_STATE_PAUSE;
        if (controller.isNavigating()) controller.cancelNavigation();
        controller.stopDisinfection();
        view.showDisinfectionSwitchTurnOffView();
        if (stayTimeMillsUntilFinished == 0) {
            currentDisinfectionPoint--;
        }
        if (durationCountDownTimer != null) durationCountDownTimer.cancel();
        if (stayCountDownTimer != null) stayCountDownTimer.cancel();
        view.showTaskStateView(currentTaskState);
    }

    /**
     * 任务定时器结束（定时任务结束)
     */
    private void onGlobalTimerFinished(Context context) {
        XLog.w("计时结束，正常结束任务");
        durationCountDownTimer = null;
        durationTimeMillsUtilFinished = 0;
        if (controller.isNavigating()) controller.cancelNavigation();
        if (stayCountDownTimer != null) {
            stayCountDownTimer.cancel();
            stayCountDownTimer = null;
        }
        onTaskFinished();
    }

    private void onTaskFinished() {
        currentTaskState = TASK_STATE_RETURNING;
        uploadTaskRecord(TaskRecord.create(task.taskName, task.taskMode, startTime.getTime(), 0, startPower, failedPoints.toString(), ""));

        if (task.switchMode == 0 || task.switchMode == 1) {
            controller.stopDisinfection();
            view.showDisinfectionSwitchTurnOffView();
        }
        VoiceHelper.play("voice_task_finish_and_go_to_charge");
        controller.chargeByPoint(mApp.getString(R.string.text_charging_pile));
        view.showReturningJourneyView(mApp.getString(R.string.text_going_to_charge));
    }

    private void uploadTaskRecord(TaskRecord taskRecord) {
        XLog.w("上传任务记录：" + taskRecord.toString());
        ServiceFactory
                .getRobotService()
                .syncTaskRecord(Event.getOnHostnameEvent().hostname, taskRecord)
                .subscribeOn(Schedulers.io())
                .observeOn(Schedulers.io())
                .subscribe(map -> Log.w("xuedong", new Gson().toJson(map)), throwable -> {

                });
    }


    @Override
    public void onNavRes(Context context, String result) {
        XLog.w(result);
        try {
            result = result.replace("nav_res:", "");
            JSONObject resultJson = new JSONObject(result);
            int res = resultJson.optInt("res");
            int reason = resultJson.optInt("reason");
            String dest = resultJson.optString("dest");
            String sensor = resultJson.optString("sensor");
            if (res == 0) {
                failedCount = 0;
                //到达充电桩
                if (context.getString(R.string.text_charging_pile).equals(dest)) {
                    controller.setChargingDocking(true);
                    VoiceHelper.play("voice_start_docking_charging_pile");
                    return;
                }
                XLog.e("dest :" + dest + ", ElevatorState.isFromOut :" + ElevatorState.isFromOut + ", nowMap :" + ElevatorState.nowFloor + ", targetMap :" + ElevatorState.goalFloor);
                if (dest.equals(ElevatorState.elevatorOut)) {
                    elevatorManager.onArriveElevatorOut();
                    if (ElevatorState.isFromOut && durationCountDownTimer != null)
                        durationCountDownTimer.cancel();
                    return;
                }
                if (dest.equals(ElevatorState.elevatorIn)) {
                    ElevatorState.isRelocating = true;
                    elevatorManager.onArriveElevatorIn();
                    currentDisinfectionPoint = 0;
                    XLog.e("到达电梯内  targetMap :" + targetMap + " applyingMap :" + ElevatorState.isRelocating);
                    return;
                }
                //到达起始点
                if (currentTaskState == TASK_STATE_RETURNING) {
                    view.showTaskFinishedView();
                    return;
                }

                //停留时间为零，直接前往下一个目标点
                if (task.stayTime == 0) {
                    gotoTargetPoint(calcNextTargetPoint());
                    return;
                }

                //停留时间不为零，如果是目标点消毒，打开消毒开关
                if (task.switchMode == 1) {
                    controller.startDisinfection();
                    view.showDisinfectionSwitchTurnOnView();
                }

                //开始停留计时
                startStayCountDownTimer(task.stayTime * 1000);
                return;
            }
            handleNavigationFailed(context, reason, sensor, dest);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void handleNavigationFailed(Context context, int reason, String sensor, String dest) {
        if (reason == 1) {
            //传感器异常 任务结束
            uploadTaskRecord(TaskRecord.create(task.taskName, task.taskMode, startTime.getTime(), 3, startPower, failedPoints.toString(), sensor));
            if (task.switchMode == 0 && currentTaskState != TASK_STATE_RETURNING) {
                //全程消毒，且不是前往充电桩或起始点，则下发停止消毒命令
                controller.stopDisinfection();
            }
            if (durationCountDownTimer != null) durationCountDownTimer.cancel();
            view.showSensorErrorView(Errors.getHardwareError(context, sensor));
        } else if (reason == 2) {
            //任务被取消
            if (!ElevatorState.isErrorOccurred || (currentTaskState != TASK_STATE_PAUSE && currentTaskState != TASK_STATE_RETURNING && currentTaskState != TASK_STATE_ELEVATOR))
                view.showTaskCanceled();
        } else if (reason == 8 || reason == 9) {

        } else {
            //其它导航异常，前往下一个点
            failedPoints.add(dest);
            if (context.getString(R.string.text_charging_pile).equals(dest) || ElevatorState.elevatorIn.equals(dest) || ElevatorState.elevatorOut.equals(dest)) {
//                if (++failedCount >= 10) {
//                    failedCount = 0;
                uploadTaskRecord(TaskRecord.create(task.taskName, task.taskMode, startTime.getTime(), 2, startPower, failedPoints.toString(), ""));
                view.showCanNotReachChargingPileView(Errors.getErrorByCode(context, reason));
                return;
//                }
//                if (context.getString(R.string.text_charging_pile).equals(dest)){
//                    controller.chargeByPoint(context.getString(R.string.text_charging_pile));
//                }else {
//                    gotoTargetPoint(dest);
//                }
//                return;
            }
            gotoTargetPoint(calcNextTargetPoint());
        }
    }

    private void startStayCountDownTimer(long timeMills) {
        stayCountDownTimer = new SimpleCountDownTimer(timeMills, 1000) {

            @Override
            public void onTick(long millisUntilFinished) {
                stayTimeMillsUntilFinished = millisUntilFinished;
            }

            @Override
            public void onFinish() {
                onStayFinished();
            }
        };
        stayCountDownTimer.start();
    }

    /**
     * 开始充电
     */
    @Override
    public void onStartCharge() {
        VoiceHelper.play("voice_start_charging");
        view.showTaskFinishedView();
        mHandler.removeCallbacks(poseRunnable);
    }

    @Override
    public void onEmergencyStopStateChange(Context context, int emergencyStopState) {
        if (emergencyStopState == 0) {
            if (ElevatorState.isPermGranted) {
                elevatorManager.free();
                elevatorManager.resetElevatorState();
            }
            XLog.w("急停开关按下，任务结束");
            if (EasyDialog.isShow()) EasyDialog.getInstance().dismiss();
            if (durationCountDownTimer != null) {
                durationCountDownTimer.cancel();
                durationCountDownTimer = null;
            }
            if (stayCountDownTimer != null) {
                stayCountDownTimer.cancel();
                stayCountDownTimer = null;
            }
            controller.stopDisinfection();
            controller.cancelNavigation();
            view.showTaskCanceled();
            if (controller.isChargingDocking()) controller.cancelCharge();
        }
    }

    @Override
    public void onPointFound() {
        canNotFindPointCount = 0;
        if (firstFoundPoint == -1) {
            firstFoundPoint = Integer.parseInt(getNextDestination());
        }
    }

    /**
     * 目标点位找不到
     *
     * @param context
     */
    @Override
    public void onPointNotFound(Context context) {
        XLog.w("未找到目标点");
        //之前找到过消毒起始点，现在找不到了（任务执行过程中，动态删除点位）
        if (targetPoint.equals(ElevatorState.elevatorIn) || targetPoint.equals(ElevatorState.elevatorOut)) {
            if (EasyDialog.isShow()) EasyDialog.getInstance().dismiss();
            elevatorManager.onError();
            elevatorManager.free();
            elevatorManager.resetElevatorState();
            view.showTaskEndByElevatorError(mApp.getString(R.string.text_can_not_find_point, targetPoint));
            return;
        }
        if (firstFoundPoint != -1 && firstFoundPoint == getCurrentDisinfectionPoint()) {
            onStartPointNotFound(context);
            return;
        }

        //五次找不到目标点
        if (++canNotFindPointCount >= 5) {

            //重置计数
            canNotFindPointCount = 0;

            //起始点都没找到
            if (-1 == firstFoundPoint) {
                onStartPointNotFound(context);
                return;
            }
            if (task.switchMode == 0) {
                controller.stopDisinfection();
                view.showDisinfectionSwitchTurnOffView();
            }
            XLog.w("单次循环，去下一楼层");
            gotoTargetFloor();
            return;
        }

        //小于五次找不到继续尝试
        gotoTargetPoint(calcNextTargetPoint());
    }

    @Override
    public void onLowPower(Context context) {
        XLog.w("任务过程中触发低电去充电");
        if (durationCountDownTimer != null) {
            durationCountDownTimer.cancel();
            durationCountDownTimer = null;
        }
        if (stayCountDownTimer != null) {
            stayCountDownTimer.cancel();
            stayCountDownTimer = null;
        }
        controller.stopDisinfection();
        currentTaskState = TASK_STATE_RETURNING;
        if (controller.isNavigating()) {
            controller.cancelNavigation();
        }
        if (!controller.isChargingDocking()) {
            if (elevatorSwitch) {
                String defaultMap = SpManager.getInstance().getString(Constant.DEFAULT_MAP, "");
                XLog.e("任务完成 :" + nowMap + " : " + defaultMap);
                if (defaultMap.equals(nowMap + "")) {
                    VoiceHelper.play("voice_low_power_go_to_charge");
                    controller.chargeByPoint(context.getString(R.string.text_charging_pile));
                    view.showReturningJourneyView(context.getString(R.string.text_going_to_charge));
                } else {
                    if (ElevatorState.isPermGranted)
                        return;
                    disinfectionFloors.clear();
                    taskFinish = true;
                    targetMap = Integer.parseInt(defaultMap);
                    gotoTargetPoint(mApp.getString(R.string.elevator_out_point));
                }
            } else {
                VoiceHelper.play("voice_low_power_go_to_charge");
                controller.chargeByPoint(context.getString(R.string.text_charging_pile));
                view.showReturningJourneyView(context.getString(R.string.text_going_to_charge));
            }
        }
    }

    @Override
    public void onMapCurrent(String map) {
        this.nowMap = Integer.parseInt(map);
        ElevatorState.nowFloor = nowMap;
    }

    @Override
    public void onApplyMapSuccess(String map) {
        this.nowMap = Integer.parseInt(map);
        ElevatorState.nowFloor = nowMap;
    }

    @Override
    public void onMarkPoint(Context context) {
        ElevatorState.isRelocating = false;
    }

    @Override
    public void onPause() {
        if (EasyDialog.isShow()) EasyDialog.getInstance().dismiss();
        if (poseRunnable != null) mHandler.removeCallbacks(poseRunnable);
        if (durationCountDownTimer != null) durationCountDownTimer.cancel();
        if (stayCountDownTimer != null) stayCountDownTimer.cancel();
        AccessController.getInstance().unInit();
        XLog.w("TaskActivity onPause");
        controller.cancelNavigation();
        controller.cancelCharge();
        if (ElevatorState.isPermGranted) {
            elevatorManager.free();
        }
        elevatorManager.resetElevatorState();
        elevatorManager.closePort();
    }

    @Override
    public void onPositionLoaded(String[] position) {
        if (!gatingSwitch) return;
        AccessController instance = AccessController.getInstance();
        if (instance == null) return;
        List<Room> rooms = Event.getOnSpecialPlanEvent().rooms;
        if (rooms == null || rooms.isEmpty()) return;
        Room room = rooms.get(0);
        List<Double> coordination = room.coordination;
        double enterX1 = coordination.get(0);
        double enterY1 = coordination.get(1);
        double enterX2 = coordination.get(2);
        double enterY2 = coordination.get(3);
        double currentX = Double.parseDouble(position[0]);
        double currentY = Double.parseDouble(position[1]);
        double distanceAB = Math.sqrt(Math.pow(enterX1 - enterX2, 2) + Math.pow(enterY1 - enterY2, 2));
        double distanceCA = Math.sqrt(Math.pow(enterX1 - currentX, 2) + Math.pow(enterY1 - currentY, 2));
        double distanceCB = Math.sqrt(Math.pow(enterX2 - currentX, 2) + Math.pow(enterY2 - currentY, 2));
        XLog.w("当前计算的特殊区域：" + room.toString() + " 特殊区域个数" + rooms.size() + " 当前门状态：" + instance.getCurrentState() + " distanceAB: " + distanceAB + " distanceCA: " + distanceCA + " distanceCB:" + distanceCB);
        boolean opposite = room.name.startsWith("N-");
        String doorNum = room.name.replace("N-", "");
        if (instance.isClosed() && distanceCA <= 2 && distanceCB > distanceCA && distanceCB >= distanceAB) {
            instance.openDoor(doorNum, opposite);
            controller.setNavSpeed("0.4");
        } else if (instance.isOpened() && distanceCB < distanceCA && distanceCB > 0.1 && distanceCA >= distanceAB) {
            instance.closeDoor(doorNum, opposite);
            controller.setNavSpeed("0.6");
        }
    }


    @Override
    public void onChargingPileNotFound(Context context) {
        XLog.w("未找到充电桩");
        mHandler.removeCallbacks(poseRunnable);
        VoiceHelper.play("voice_not_found_charging_pile");
        view.showChargingPileNotFound();
    }

    private void onStartPointNotFound(Context context) {
        XLog.w("未找到消毒起始点，异常结束任务");
        mHandler.removeCallbacks(poseRunnable);
        if (task.switchMode == 0) {
            controller.stopDisinfection();
            view.showDisinfectionSwitchTurnOffView();
        }

        if (durationCountDownTimer != null) {
            durationCountDownTimer.cancel();
            durationCountDownTimer = null;
        }

        EasyDialog.getInstance(context).warn(context.getString(R.string.text_can_not_find_starting_point), new EasyDialog.OnViewClickListener() {
            @Override
            public void onViewClick(Dialog dialog, int id) {
                dialog.dismiss();
                view.showTaskFinishedView();
            }
        });
    }

    /**
     * 停留完成
     */
    private void onStayFinished() {
        stayCountDownTimer = null;
        stayTimeMillsUntilFinished = 0;
        if (task.switchMode == 1) {
            controller.stopDisinfection();
            view.showDisinfectionSwitchTurnOffView();
        }
        gotoTargetPoint(calcNextTargetPoint());
    }


    @Override
    public void onOpenDoorSuccess() {
        if (AccessController.getInstance().getCurrentState() == AccessController.State.OPENED)
            return;
        XLog.w("开门成功");
        AccessController.getInstance().setCurrentState(AccessController.State.OPENED);
        view.showOpenDoorSuccessView();
    }

    @Override
    public void onCloseDoorSuccess(String currentClosingDoor) {
        if (AccessController.getInstance().getCurrentState() == AccessController.State.CLOSED)
            return;
        XLog.w("关门成功");
        List<Room> rooms = Event.getOnSpecialPlanEvent().rooms;
        if (rooms != null && !rooms.isEmpty()) {
            Room room = rooms.get(0);
            if (room.name.equals(currentClosingDoor))
                rooms.remove(0);
        }
        AccessController.getInstance().setCurrentState(AccessController.State.CLOSED);
        view.showCloseDoorSuccessView();
    }

    @Override
    public void onPermissionRelease() {
        if (ElevatorState.isErrorOccurred)return;
        view.onElevatorDialogDismiss();
        if (taskFinish) {
            mHandler.post(this::onTaskFinished);
            return;
        }

        //全程打开消毒则任务开始时打开消毒开关
        if (task.switchMode == 0) {
            mHandler.postDelayed(() -> {
                controller.startDisinfection();
                view.showDisinfectionSwitchTurnOnView();
            }, 5000);
        }
        currentTaskState = TASK_STATE_RUNNING;
        disinfectionFloors.remove(targetMap + "");
        mHandler.post(() -> {
            gotoTargetPoint(calcNextTargetPoint());
            startDurationCountdownTimer(mApp, durationTimeMillsUtilFinished);
        });
    }

    @Override
    public void onPortOpenFailed(Exception e) {
        controller.cancelNavigation();
        view.showOnPortOpenFailed(e);
    }

    @Override
    public void onWebSocketAuthFailed(String errorMsg) {

    }

    @Override
    public void onPermissionRequestFailed() {
        view.onPermissionRequestFailed();
    }

    @Override
    public void onError(String data) {
        if (EasyDialog.isShow()) EasyDialog.getInstance().dismiss();
        elevatorManager.onError();
        elevatorManager.free();
        elevatorManager.resetElevatorState();
        controller.cancelNavigation();
        view.showTaskEndByElevatorError(data);
    }

    @Override
    public void onWebSocketConnectFailed() {

    }

    @Override
    public void onWebSocketError(String msg) {

    }

    @Override
    public void onPermissionSuccess() {
        view.onShowElevatorDialog(mApp.getString(R.string.text_permission_success));
    }

    @Override
    public void onNaviIntoElevator() {
        view.onShowElevatorDialog(mApp.getString(R.string.text_elevator_arrive));
    }

    @Override
    public void onApplyingMap() {
        view.onShowElevatorDialog(mApp.getString(R.string.text_arrive_target_floor, ElevatorState.elevatorIn));
    }

    @Override
    public void onLeaveElevator() {
        view.onShowElevatorDialog(mApp.getString(R.string.text_going_to_elevator_out));
    }
}
