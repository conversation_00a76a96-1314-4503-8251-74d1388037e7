package com.reeman.robot.disinfection.presenter.impl;

import android.content.Context;
import android.text.TextUtils;

import com.reeman.robot.disinfection.R;
import com.reeman.robot.disinfection.constants.Constant;
import com.reeman.robot.disinfection.contract.TaskCreateContract;
import com.reeman.robot.disinfection.event.Event;
import com.reeman.robot.disinfection.repository.entities.Task;
import com.reeman.robot.disinfection.request.factory.ServiceFactory;
import com.reeman.robot.disinfection.request.model.OnlineTask;
import com.reeman.robot.disinfection.request.model.TaskUpdateResponse;
import com.reeman.robot.disinfection.utils.SpManager;
import com.reeman.robot.disinfection.utils.TimeUtils;

import java.util.Date;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.annotations.NonNull;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.SingleObserver;
import io.reactivex.rxjava3.disposables.Disposable;
import io.reactivex.rxjava3.functions.Consumer;
import io.reactivex.rxjava3.schedulers.Schedulers;

import static com.reeman.robot.disinfection.base.BaseApplication.dbRepository;

public class TaskCreatePresenter implements TaskCreateContract.Presenter {

    private final TaskCreateContract.View view;

    private Task currentTask;

    public TaskCreatePresenter(TaskCreateContract.View view) {
        this.view = view;
    }

    public void setCurrentTask(Task currentTask) {
        this.currentTask = currentTask;
    }

    public Task getCurrentTask() {
        return currentTask;
    }

    @Override
    public void newTask(Context context, String taskName, int cycleMode, int backMode, int switchMode, String stayTime, String durationTime, String startTime, int repeatMode,String disinfectionFloors) {
        long calcStayTime = TimeUtils.str2Time(stayTime);
        long calcDurationTime = TimeUtils.str2Time(durationTime);

        //定时任务要选择日期
        if (currentTask.taskType == 1 && repeatMode == 0) {
            view.onTaskSaveFailed(context.getString(R.string.text_invalid_repeat_time));
            return;
        }

        //定时任务名称不能为空，手动任务名称固定
        if (currentTask.taskType == 1 && TextUtils.isEmpty(taskName)) {
            view.onTaskSaveFailed(context.getString(R.string.text_invalid_task_name));
            return;
        }

        //循环时长大于五秒
        if (cycleMode == R.id.rb_duration_mode && calcDurationTime < 5) {
            view.onTaskSaveFailed(context.getString(R.string.text_invalid_time_in_duration_mode));
            return;
        }

        //停留时长为零时不能选择目标点消毒
        if (switchMode == R.id.rb_open_in_target_point && calcStayTime == 0) {
            view.onTaskSaveFailed(context.getString(R.string.text_open_in_target_point_when_stay_time_is_0));
            return;
        }

        Date createDate = new Date();
        Task task = new Task(
                currentTask.cloudId,
                currentTask.taskType,
                currentTask.taskType == 0 ? Constant.MANUAL_TASK_NAME : taskName,
                cycleMode == R.id.rb_single_mode ? 0 : 1,
                switchMode == R.id.rb_open_all_the_way ? 0 : (switchMode == R.id.rb_open_in_target_point ? 1 : 2),
                backMode == R.id.rb_charge_when_finished ? 0 : 1,
                calcStayTime,
                calcDurationTime,
                currentTask.taskType == 0 ? "" : startTime,
                repeatMode,
                true,
                createDate,
                disinfectionFloors
        );

        if (task.taskType == 1)
            view.showSavingTaskView();

        if (currentTask.tid != -1) {
            task.tid = currentTask.tid;
            dbRepository.updateTask(task)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(new SingleObserver<Integer>() {
                        @Override
                        public void onSubscribe(@NonNull Disposable d) {

                        }

                        @Override
                        public void onSuccess(@NonNull Integer affectCount) {

                            //手动任务直接结束
                            if (task.taskType == 0) {
                                view.onTaskSaved();
                                return;
                            }
                            //定时任务更新在线内容
                            createOrUpdateOnlineTask(task, affectCount, false);
                        }

                        @Override
                        public void onError(@NonNull Throwable e) {
                        }
                    });
        } else {
            dbRepository.createTask(task)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(new SingleObserver<Long>() {
                        @Override
                        public void onSubscribe(@NonNull Disposable d) {

                        }

                        @Override
                        public void onSuccess(@NonNull Long rowId) {
                            //手动任务直接结束
                            if (task.taskType == 0) {
                                view.onTaskSaved();
                                return;
                            }
                            //定时任务更新在线内容
                            createOrUpdateOnlineTask(task, rowId, true);
                        }

                        @Override
                        public void onError(@NonNull Throwable e) {
                        }
                    });
        }


    }

    private void createOrUpdateOnlineTask(Task task, long row, boolean isInsertData) {
        //没登录
        if (Constant.DEFAULT_USERNAME.equals(SpManager.getInstance().getString(Constant.USERNAME, Constant.DEFAULT_USERNAME))) {
            view.onTaskSaved();
            return;
        }

        Observable<TaskUpdateResponse> observable;

        OnlineTask onlineTask = OnlineTask.covertBy(task);

        if (task.cloudId == -1) {
            //创建在线任务
            observable = ServiceFactory
                    .getRobotService()
                    .newOnlineTask(Event.getOnHostnameEvent().hostname, onlineTask);
        } else {
            //更新在线任务
            observable = ServiceFactory
                    .getRobotService()
                    .updateOnlineTask(Event.getOnHostnameEvent().hostname, task.cloudId, onlineTask);
        }


        observable.subscribeOn(Schedulers.io())
                .observeOn(Schedulers.io())
                .subscribe(new Consumer<TaskUpdateResponse>() {
                    @Override
                    public void accept(TaskUpdateResponse response) throws Throwable {
                        if (isInsertData) {
                            dbRepository.updateLocalTaskOnlineStateByRowId(row, response.data.result.id);
                        } else {
                            dbRepository.updateLocalTaskOnlineStateByPrimaryKey(task.tid, response.data.result.id);
                        }
                        view.onTaskSaved();
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Throwable {
                        view.onTaskSaved();
                    }
                });
    }

}
