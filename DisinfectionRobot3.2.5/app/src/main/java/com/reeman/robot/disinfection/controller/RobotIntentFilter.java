package com.reeman.robot.disinfection.controller;

import android.content.Intent;
import android.content.IntentFilter;

public class RobotIntentFilter extends IntentFilter {
    public RobotIntentFilter(){
        addActions();
    }

    private void addActions() {
        addAction(Intent.ACTION_BATTERY_CHANGED);
        addAction(Intent.ACTION_POWER_CONNECTED);
        addAction(Intent.ACTION_POWER_DISCONNECTED);
        addAction(Intent.ACTION_TIME_TICK);
        addAction("REEMAN_BROADCAST_WAKEUP");
        addAction("REEMAN_LAST_MOVTION");
        addAction("REEMAN_BROADCAST_SCRAMSTATE");
        addAction("ACTION_POWER_CONNECTE_REEMAN");
        addAction("AUTOCHARGE_ERROR_DOCKNOTFOUND");
        addAction("AUTOCHARGE_ERROR_DOCKINGFAILURE");
        addAction("android.net.conn.CONNECTIVITY_CHANGE");
    }
}
