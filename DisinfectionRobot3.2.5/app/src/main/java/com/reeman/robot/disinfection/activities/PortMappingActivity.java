package com.reeman.robot.disinfection.activities;

import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.reeman.robot.disinfection.R;
import com.reeman.robot.disinfection.adapters.PortMappingItemAdapter;
import com.reeman.robot.disinfection.base.BaseActivity;
import com.reeman.robot.disinfection.constants.Constant;
import com.reeman.robot.disinfection.request.model.PortMapping;
import com.reeman.robot.disinfection.utils.ScreenUtils;
import com.reeman.robot.disinfection.utils.SoftKeyboardStateWatcher;
import com.reeman.robot.disinfection.utils.SpManager;
import com.reeman.robot.disinfection.utils.ToastUtils;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

public class PortMappingActivity extends BaseActivity implements View.OnClickListener, PortMappingItemAdapter.PortMappingItemListener, SoftKeyboardStateWatcher.SoftKeyboardStateListener {

    private RecyclerView rvPortMappingList;

    private PortMappingItemAdapter portMappingItemAdapter;

    private List<PortMapping> portMappingList;

    @Override
    protected int getLayoutRes() {
        return R.layout.activity_port_mapping;
    }

    @Override
    protected void initView() {
        super.initView();
        rvPortMappingList = $(R.id.rv_port_mapping_list);
        $(R.id.ib_add).setOnClickListener(this);
        $(R.id.tv_back).setOnClickListener(this);
    }

    @Override
    protected void initData() {
        super.initData();
        SoftKeyboardStateWatcher softKeyboardStateWatcher = new SoftKeyboardStateWatcher(getWindow().getDecorView());
        softKeyboardStateWatcher.addSoftKeyboardStateListener(this);
        rvPortMappingList.setLayoutManager(new LinearLayoutManager(this));
        portMappingList = new ArrayList<>();
        String portMappingStr = SpManager.getInstance().getString(Constant.PORT_MAPPING, null);
        if (portMappingStr == null || portMappingStr.length() < 20) {
            portMappingList.add(new PortMapping(0, 1));
        } else {
            Type type = new TypeToken<ArrayList<PortMapping>>() {
            }.getType();
            portMappingList = new Gson().fromJson(portMappingStr, type);
        }
        portMappingItemAdapter = new PortMappingItemAdapter(portMappingList);
        rvPortMappingList.setAdapter(portMappingItemAdapter);
        portMappingItemAdapter.setPortMappingItemListener(this);
    }

    @Override
    protected void onResume() {
        super.onResume();
    }

    @Override
    protected void onPause() {
        super.onPause();
    }

    @Override
    public void onClick(View view) {
        portMappingList = portMappingItemAdapter.getPortMappingList();
        switch (view.getId()) {
            case R.id.ib_add:
                if (portMappingList.size() > 0) {
                    PortMapping portMapping = portMappingList.get(portMappingList.size() - 1);
                    portMappingList.add(new PortMapping(portMapping.getPort() + 1, portMapping.getFloor() + 1));
                } else {
                    portMappingList.add(new PortMapping(0, 1));
                }
                portMappingItemAdapter.notifyItemInserted(portMappingItemAdapter.getItemCount());
                break;
            case R.id.tv_back:
                for (int i = 0; i < portMappingList.size(); i++) {
                    for (int j = 0; j < portMappingList.size(); j++) {
                        if (i == j) continue;
                        if (portMappingList.get(i).getPosition() == portMappingList.get(j).getPosition()) {
                            portMappingList.set(i, portMappingList.get(j));
                            continue;
                        }
                        if (portMappingList.get(i).getFloor() == portMappingList.get(j).getFloor() || portMappingList.get(i).getPort() == portMappingList.get(j).getPort()) {
                            ToastUtils.showShortToast(getString(R.string.text_port_or_floor_can_not_same));
                            return;
                        }
                    }
                }
                SpManager.getInstance().edit().putString(Constant.PORT_MAPPING, new Gson().toJson(portMappingList)).apply();
                finish();
                break;
        }
    }


    @Override
    public void onChange(int position, PortMapping portMapping) {
//        for (int i = 0; i < portMappingList.size(); i++) {
//            if (portMappingList.get(i).getPosition() == portMapping.getPosition()) {
//                portMappingList.set(i, portMapping);
//            }
//        }
    }

//    @Override
//    public void onDelete(int position) {
//        portMappingList.remove(position);
//        portMappingItemAdapter.notifyItemRemoved(position);
//        portMappingItemAdapter.notifyItemRangeChanged(position, portMappingList.size() - position);
//    }

    @Override
    public void onSoftKeyboardOpened(int keyboardHeightInPx) {

    }

    @Override
    public void onSoftKeyboardClosed() {
        ScreenUtils.hideBottomUIMenu(this);
    }
}
