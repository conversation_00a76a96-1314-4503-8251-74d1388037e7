package com.reeman.robot.disinfection.activities;

import android.view.View;
import android.widget.Button;
import android.widget.EditText;

import com.reeman.robot.disinfection.R;
import com.reeman.robot.disinfection.base.BaseActivity;
import com.reeman.robot.disinfection.constants.Constant;
import com.reeman.robot.disinfection.contract.LoginContract;
import com.reeman.robot.disinfection.presenter.impl.LoginPresenter;
import com.reeman.robot.disinfection.utils.ScreenUtils;
import com.reeman.robot.disinfection.utils.SoftKeyboardStateWatcher;
import com.reeman.robot.disinfection.utils.ToastUtils;
import com.reeman.robot.disinfection.utils.VoiceHelper;
import com.reeman.robot.disinfection.widgets.EasyDialog;

public class LoginActivity extends BaseActivity implements View.OnClickListener, LoginContract.View, SoftKeyboardStateWatcher.SoftKeyboardStateListener {

    private EditText etUsername;
    private EditText etPassword;
    private LoginPresenter presenter;
    private String from;
    private int finishResult;
    private SoftKeyboardStateWatcher softKeyboardStateWatcher;

    @Override
    protected boolean shouldResponse2TimeEvent() {
        return from == null;
    }


    @Override
    public void onBackPressed() {
    }

    @Override
    protected int getLayoutRes() {
        return R.layout.activity_login;
    }


    @Override
    protected void initView() {
        etUsername = $(R.id.et_username);
        etPassword = $(R.id.et_password);

        Button btnLogin = $(R.id.btn_login);
        Button btnSkip = $(R.id.btn_skip);
        btnLogin.setOnClickListener(this);
        btnSkip.setOnClickListener(this);
    }

    @Override
    protected void initData() {
        softKeyboardStateWatcher = new SoftKeyboardStateWatcher(getWindow().getDecorView());
        softKeyboardStateWatcher.addSoftKeyboardStateListener(this);
        presenter = new LoginPresenter(this);
        from = getIntent().getStringExtra(Constant.EXTRA);
        finishResult = getIntent().getIntExtra(Constant.EXTRA_INT, 0);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        softKeyboardStateWatcher.removeSoftKeyboardStateListener(this);
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        switch (id) {
            case R.id.btn_login:
                presenter.login(this, etUsername.getText().toString().trim(), etPassword.getText().toString().trim());
                break;
            case R.id.btn_skip:
                presenter.onSkip(this, from, finishResult);
                break;
        }
    }


    @Override
    public void onLoginEvent(String string, boolean result) {
        ToastUtils.showShortToast(string);
        //登录成功
        if (result) {
            VoiceHelper.play("voice_login_success", new VoiceHelper.OnCompleteListener() {
                @Override
                public void onComplete() {
                    if (EasyDialog.isShow()) EasyDialog.getInstance().dismiss();
                    if (GuideActivity.class.getSimpleName().equals(from)) {
                        setResult(finishResult);
                    }
                    finish();
                }
            });
            return;
        }
        //登录失败
        mHandler.postDelayed(() -> {
            if (EasyDialog.isShow()) EasyDialog.getInstance().dismiss();
            etPassword.setText("");
        }, 1000);
    }

    @Override
    public void onSoftKeyboardOpened(int keyboardHeightInPx) {

    }

    @Override
    public void onSoftKeyboardClosed() {
        ScreenUtils.hideBottomUIMenu(this);
    }
}