package com.reeman.robot.disinfection.controller;

import android.os.Handler;
import android.os.HandlerThread;


import com.elvishew.xlog.XLog;
import com.reeman.robot.disinfection.constants.Constant;
import com.reeman.robot.disinfection.constants.MsgType;
import com.reeman.robot.disinfection.listener.ElevatorListener;
import com.reeman.robot.disinfection.request.model.ElevatorModel;
import com.reeman.robot.disinfection.utils.Crc16Utils;
import com.reeman.robot.disinfection.utils.SpManager;

import java.io.IOException;
import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetAddress;
import java.net.SocketException;
import java.util.concurrent.ExecutorService;


public class ElevatorController {
    private final static int CALL_OUT_EVENT = 1;
    private final static int GET_ELEVATOR_EVENT = 2;
    private final static int CALL_IN_EVENT = 3;
    private final String TAG = getClass().getSimpleName();
    private DatagramSocket dsMain;
    private DatagramSocket dsOut;
    private String mainIP, outIP;
    private int mainPort, outPort;
    private ElevatorListener elevatorListener;
    private Handler handler;

    private static ElevatorController elevatorController;

    public static ElevatorController getInstance() {
        if (elevatorController == null) {
            synchronized (ElevatorController.class) {
                if (elevatorController == null) {
                    elevatorController = new ElevatorController();
                }
            }
        }
        return elevatorController;
    }

    public void setMainIP(String ip) {
        SpManager.encode(Constant.SP_MAIN_IP, ip);
    }

    public String getMainIP() {
        return SpManager.decodeString(Constant.SP_MAIN_IP, Constant.DEFAULT_MAIN_IP);
    }

    public void setOutIP(String ip) {
        SpManager.encode(Constant.SP_OUT_IP, ip);
    }

    public String getOutIP() {
        return SpManager.decodeString(Constant.SP_OUT_IP, Constant.DEFAULT_OUT_IP);
    }

    public void init(ElevatorListener elevatorListener) {
        this.elevatorListener = elevatorListener;
        initIPAndPort();
        HandlerThread thread = new HandlerThread("ElevatorThread");
        thread.start();
        handler = new Handler(thread.getLooper());
    }

    private void initIPAndPort() {
        if (outIP == null || outIP.equals("")) {
            outIP = getOutIP();
            String[] split = outIP.split(":");
            outIP = split[0];
            outPort = Integer.parseInt(split[1]);
            e("initOutIp: " + outIP + ":" + outPort);
        }
        if (mainIP == null || mainIP.equals("")) {
            mainIP = getMainIP();
            String[] split = mainIP.split(":");
            mainIP = split[0];
            mainPort = Integer.parseInt(split[1]);
            e("initMainIp: " + mainIP + ":" + mainPort);
        }
    }

    public void getElevatorState() {
        byte[] bytes = new byte[]{0x00, 0x00, 0x00, 0x24, 0x00, 0x00};
        bytes = Crc16Utils.getData(Crc16Utils.getLength(bytes));
        e("获取状态 :" + Crc16Utils.byteTo16String(bytes) + (mainPort == outPort));
        handler.postDelayed(isTimeOutRunnable.setEventType(GET_ELEVATOR_EVENT), 1000);
        handler.post(getElevatorStateRunnable.setParam(mainPort == outPort ? dsOut : dsMain, bytes));
    }

    public void callElevatorOut(int type, int frontFloor, int backFloor) {
        byte[] bytes = new byte[]{0x00, 0x00, 0x12, 0x11, 0x00, 0x00, (byte) type, 0x00, (byte) backFloor, 0x00, (byte) frontFloor};
        bytes = Crc16Utils.getData(Crc16Utils.getLength(bytes));
        e("梯外呼梯 :" + Crc16Utils.byteTo16String(bytes));
        handler.postDelayed(isTimeOutRunnable.setEventType(CALL_OUT_EVENT), 1000);
        handler.post(callElevatorOutRunnable.setParam(dsOut, bytes));
    }

    public void callElevatorIn(int frontFloor, int backFloor) {
        byte[] bytes = new byte[]{0x00, 0x00, 0x12, 0x0a, 0x00, 0x00, 0x00, (byte) backFloor, 0x00, (byte) frontFloor};
        bytes = Crc16Utils.getData(Crc16Utils.getLength(bytes));
        e("梯内呼梯: " + Crc16Utils.byteTo16String(bytes) + (mainPort == outPort));
        handler.postDelayed(isTimeOutRunnable.setEventType(CALL_IN_EVENT), 1000);
        handler.post(callElevatorInRunnable.setParam(mainPort == outPort ? dsOut : dsMain, bytes));
    }

    SendRunnable getElevatorStateRunnable = new SendRunnable() {

        private DatagramSocket ds;
        private byte[] bytes;

        @Override
        public SendRunnable setParam(DatagramSocket ds, byte[] bytes) {
            this.ds = ds;
            this.bytes = bytes;
            return this;
        }

        @Override
        public void run() {
            try {
                DatagramPacket dp = new DatagramPacket(bytes, bytes.length, InetAddress.getByName(mainIP), mainPort);
                ds.send(dp);
            } catch (IOException e) {
                e("获取状态 :" + e.getMessage());
                elevatorListener.getElevatorStateEvent(new ElevatorModel(MsgType.IO_EXCEPTION, e.getMessage()));
                handler.removeCallbacksAndMessages(null);
            }
        }
    };

    SendRunnable callElevatorOutRunnable = new SendRunnable() {

        private DatagramSocket ds;
        private byte[] bytes;

        @Override
        public SendRunnable setParam(DatagramSocket ds, byte[] bytes) {
            this.ds = ds;
            this.bytes = bytes;
            return this;
        }

        @Override
        public void run() {
            try {
                DatagramPacket dp = new DatagramPacket(bytes, bytes.length, InetAddress.getByName(outIP), outPort);
                ds.send(dp);
            } catch (IOException e) {
                e("梯外呼梯 :" + e.getMessage());
                elevatorListener.callElevatorOutEvent(MsgType.IO_EXCEPTION);
                handler.removeCallbacksAndMessages(null);
            }
        }
    };

    SendRunnable callElevatorInRunnable = new SendRunnable() {

        private DatagramSocket ds;
        private byte[] bytes;

        @Override
        public SendRunnable setParam(DatagramSocket ds, byte[] bytes) {
            this.ds = ds;
            this.bytes = bytes;
            return this;
        }

        @Override
        public void run() {
            try {
                DatagramPacket dp = new DatagramPacket(bytes, bytes.length, InetAddress.getByName(mainIP), mainPort);
                ds.send(dp);
            } catch (IOException e) {
                e("梯内呼梯 :" + e.getMessage());
                elevatorListener.callElevatorInEvent(MsgType.IO_EXCEPTION);
                handler.removeCallbacksAndMessages(null);
            }
        }
    };

    public interface SendRunnable extends Runnable {
        SendRunnable setParam(DatagramSocket ds, byte[] bytes);
    }

    public void initReceive() {
        if (elevatorListener == null) return;
        if (outIP.equals("") || outPort == 0 || mainIP.equals("") || mainPort == 0) {
            elevatorListener.initEvent(MsgType.IP_OR_PORT_NULL);
            return;
        }
        try {
            dsOut = new DatagramSocket(outPort);
            ReceiveDataFromOutThread receiveDataFromOutThread = new ReceiveDataFromOutThread(dsOut);
            receiveDataFromOutThread.start();
            if (outPort != mainPort) {
                dsMain = new DatagramSocket(mainPort);
                ReceiveDataFromMainThread receiveDataFromMainThread = new ReceiveDataFromMainThread(dsMain);
                receiveDataFromMainThread.start();
            }
        } catch (IOException e) {
            e("initReceive :初始化失败");
            elevatorListener.initEvent(MsgType.IO_EXCEPTION);
        }finally {
            elevatorListener.initEvent(MsgType.SUCCESS);
        }
        e("initReceive :接收消息");
    }

    class ReceiveDataFromOutThread extends Thread {

        private final DatagramSocket dsOut;

        public ReceiveDataFromOutThread(DatagramSocket dsOut) {
            this.dsOut = dsOut;
        }

        @Override
        public void run() {
            byte[] byteReceive = new byte[2048];
            DatagramPacket dpReceive = new DatagramPacket(byteReceive, byteReceive.length);
            try {
                while (handler != null && elevatorListener != null && dsOut != null) {
                    dsOut.receive(dpReceive);
                    e("ReceiveData" + Crc16Utils.byteTo16String(dpReceive.getData()).substring(0, 60));
                    handler.removeCallbacksAndMessages(null);
                    byte[] data = dpReceive.getData();
                    String substring = Crc16Utils.byteTo16String(data).substring(0, 60);
                    if (substring.startsWith("12 11", 18)) {
                        e("梯外呼梯Receive :" + substring);
                        if (data[9] == 0) {
                            elevatorListener.callElevatorOutEvent(MsgType.SUCCESS);
                        } else {
                            elevatorListener.callElevatorOutEvent(MsgType.ORDER_FAILED);
                        }
                    } else if (substring.startsWith("00 24", 18)) {
                        e("获取状态Receive :" + substring);
                        if (data[9] == 0) {
                            elevatorListener.getElevatorStateEvent(new ElevatorModel(MsgType.SUCCESS, data[10],
                                    (short) Crc16Utils.bytesToInt(data[11], data[12]), data[13], data[14]));
                        } else {
                            elevatorListener.getElevatorStateEvent(new ElevatorModel(MsgType.ORDER_FAILED, ""));
                        }

                    } else if (substring.startsWith("12 0a", 18)) {
                        e("梯内呼梯Receive :" + substring);
                        if (data[9] == 0) {
                            elevatorListener.callElevatorInEvent(MsgType.SUCCESS);
                        } else {
                            elevatorListener.callElevatorInEvent(MsgType.ORDER_FAILED);
                        }
                    }
                }
            } catch (IOException e) {
                e("ReceiveException :" + e.getMessage());
                if (elevatorListener == null) return;
                elevatorListener.initEvent(MsgType.IO_EXCEPTION);
            }
        }
    }

    class ReceiveDataFromMainThread extends Thread {
        private final DatagramSocket dsMain;

        public ReceiveDataFromMainThread(DatagramSocket dsMain) {
            this.dsMain = dsMain;
        }

        @Override
        public void run() {
            byte[] byteReceive = new byte[2048];
            DatagramPacket dpReceive = new DatagramPacket(byteReceive, byteReceive.length);
            try {
                while (handler!= null && elevatorListener != null && dsMain != null) {
                    dsMain.receive(dpReceive);
                    handler.removeCallbacksAndMessages(null);
                    byte[] data = dpReceive.getData();
                    String substring = Crc16Utils.byteTo16String(data).substring(0, 60);
                    if (substring.startsWith("00 24", 18)) {
                        e("获取状态Receive :" + substring);
                        if (data[9] == 0) {
                            elevatorListener.getElevatorStateEvent(new ElevatorModel(MsgType.SUCCESS, data[10],
                                    (short) Crc16Utils.bytesToInt(data[11], data[12]), data[13], data[14]));
                        } else {
                            elevatorListener.getElevatorStateEvent(new ElevatorModel(MsgType.ORDER_FAILED, ""));
                        }

                    } else if (substring.startsWith("12 0a", 18)) {
                        e("梯内呼梯Receive :" + substring);
                        if (data[9] == 0) {
                            elevatorListener.callElevatorInEvent(MsgType.SUCCESS);
                        } else {
                            elevatorListener.callElevatorInEvent(MsgType.ORDER_FAILED);
                        }
                    }
                }
            } catch (IOException e) {
                if (elevatorListener == null)return;
                elevatorListener.initEvent(MsgType.IO_EXCEPTION);
            }
        }
    }


    public interface TimeOutRunnable extends Runnable {
        TimeOutRunnable setEventType(int type);
    }

    TimeOutRunnable isTimeOutRunnable = new TimeOutRunnable() {
        private int type;

        @Override
        public TimeOutRunnable setEventType(int type) {
            this.type = type;
            return this;
        }

        @Override
        public void run() {
            if (type == CALL_OUT_EVENT) {
                elevatorListener.callElevatorOutEvent(MsgType.NO_RECEIVE);
            } else if (type == GET_ELEVATOR_EVENT) {
                elevatorListener.getElevatorStateEvent(new ElevatorModel(MsgType.NO_RECEIVE, ""));
            } else if (type == CALL_IN_EVENT) {
                elevatorListener.callElevatorInEvent(MsgType.NO_RECEIVE);
            }
        }
    };


    public void unInit() {
        mainIP = "";
        mainPort = 0;
        outIP = "";
        outPort = 0;
        elevatorListener = null;
        if (dsMain != null)
            dsMain.close();
        if (dsOut != null)
            dsOut.close();
        dsMain = null;
        dsOut = null;
        if (handler != null)
            handler.removeCallbacksAndMessages(null);
        handler = null;
    }

    private void e(Object obj) {
        XLog.tag(TAG).e(obj);
    }

}
