package com.reeman.robot.disinfection.widgets;

import android.text.Editable;
import android.text.TextWatcher;

public abstract class EasyTextWatcher implements TextWatcher {

    public abstract void onTextChanged(CharSequence charSequence);

    @Override
    public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

    }

    @Override
    public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
        onTextChanged(charSequence);
    }

    @Override
    public void afterTextChanged(Editable editable) {

    }


}
