package com.reeman.robot.disinfection.request.service;

import com.reeman.robot.disinfection.request.model.BaseResponse;
import com.reeman.robot.disinfection.request.model.LoginResponse;
import com.reeman.robot.disinfection.request.model.MapListResponse;
import com.reeman.robot.disinfection.request.model.OnlineTask;
import com.reeman.robot.disinfection.request.model.State;
import com.reeman.robot.disinfection.request.model.TaskListResponse;
import com.reeman.robot.disinfection.request.model.TaskRecord;
import com.reeman.robot.disinfection.request.model.TaskUpdateResponse;

import java.util.List;
import java.util.Map;

import io.reactivex.rxjava3.core.Observable;
import okhttp3.MultipartBody;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.DELETE;
import retrofit2.http.GET;
import retrofit2.http.Multipart;
import retrofit2.http.PATCH;
import retrofit2.http.POST;
import retrofit2.http.PUT;
import retrofit2.http.Part;
import retrofit2.http.Path;
import retrofit2.http.Url;

public interface RobotService {
    @POST("/openapispring/tokens")
    Observable<LoginResponse> login(@Body Map<String, String> loginModel);

    @GET
    Observable<MapListResponse> getMapList(@Url String url);

    @POST("/openapispring/tokens")
    Call<LoginResponse> loginSync(@Body Map<String, String> loginModel);

    @PUT("/openapispring/disinfectionrobots/{hostname}")
    Call<Map<String, Object>> syncState(@Body State state, @Path("hostname") String hostname);

    @GET("/openapispring/disinfectionrobots/{hostname}/tasks?page=1&size=100")
    Call<TaskListResponse> getAllOnlineTask(@Path("hostname") String hostname);

    @POST("/openapispring/disinfectionrobots/{hostname}/tasks")
    Observable<TaskUpdateResponse> newOnlineTask(@Path("hostname") String hostname, @Body OnlineTask onlineTask);

    @POST("/openapispring/disinfectionrobots/{hostname}/tasks")
    Call<TaskUpdateResponse> newOnlineTaskSync(@Path("hostname") String hostname, @Body OnlineTask onlineTask);

    @PUT("/openapispring/disinfectionrobots/{hostname}/tasks/{taskId}")
    Observable<TaskUpdateResponse> updateOnlineTask(@Path("hostname") String hostname, @Path("taskId") long taskId, @Body OnlineTask onlineTask);

    @PUT("/openapispring/disinfectionrobots/{hostname}/tasks/{taskId}")
    Call<TaskUpdateResponse> updateOnlineTaskSync(@Path("hostname") String hostname, @Path("taskId") long taskId, @Body OnlineTask onlineTask);

    @PATCH("/openapispring/disinfectionrobots/{hostname}/tasks/{taskId}")
    Call<TaskUpdateResponse> updateOnlineTaskEnableState(@Path("hostname") String hostname, @Path("taskId") long taskId, @Body Map<String, Integer> map);

    @DELETE("/openapispring/disinfectionrobots/{hostname}/tasks/{taskId}")
    Call<BaseResponse> deleteOnlineTask(@Path("hostname") String hostname, @Path("taskId") long taskId);

    @POST("/openapispring/disinfectionrobots/{hostname}/tasks/logs")
    Observable<Map<String, String>> syncTaskRecord(@Path("hostname") String hostname, @Body TaskRecord record);
}
