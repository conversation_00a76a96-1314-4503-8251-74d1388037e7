package com.reeman.robot.disinfection.repository.entities;

import android.content.Context;
import android.os.Parcel;
import android.os.Parcelable;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.PrimaryKey;

import com.reeman.robot.disinfection.R;
import com.reeman.robot.disinfection.constants.Constant;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

/**
 * 任务类
 */
@Entity(tableName = "t_task")
public class Task implements Parcelable {

    public Task() {

    }


    protected Task(Parcel in) {
        tid = in.readLong();
        cloudId = in.readLong();
        taskType = in.readInt();
        taskName = in.readString();
        taskMode = in.readInt();
        switchMode = in.readInt();
        finishAction = in.readInt();
        stayTime = in.readLong();
        durationTime = in.readLong();
        startTime =  in.readString();
        repeatTime = in.readInt();
        enabled = in.readByte() != 0;
        createTime = (Date) in.readSerializable();
        hasSync = in.readByte() != 0;
        hasDelete = in.readByte() != 0;
        disinfectionFloors = in.readString();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeLong(tid);
        dest.writeLong(cloudId);
        dest.writeInt(taskType);
        dest.writeString(taskName);
        dest.writeInt(taskMode);
        dest.writeInt(switchMode);
        dest.writeInt(finishAction);
        dest.writeLong(stayTime);
        dest.writeLong(durationTime);
        dest.writeString(startTime);
        dest.writeInt(repeatTime);
        dest.writeByte((byte) (enabled ? 1 : 0));
        dest.writeSerializable(createTime);
        dest.writeByte((byte) (hasSync ? 1 : 0));
        dest.writeByte((byte) (hasDelete ? 1 : 0));
        dest.writeString(disinfectionFloors);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<Task> CREATOR = new Creator<Task>() {
        @Override
        public Task createFromParcel(Parcel in) {
            return new Task(in);
        }

        @Override
        public Task[] newArray(int size) {
            return new Task[size];
        }
    };


    @Ignore
    public Task(long cloudId, int taskType, String taskName, int taskMode, int switchMode, int finishAction, long stayTime, long durationTime, String startTime, int repeatTime, boolean enabled, Date createTime, boolean hasSync, boolean hasDelete) {
        this.cloudId = cloudId;
        this.taskType = taskType;
        this.taskName = taskName;
        this.taskMode = taskMode;
        this.switchMode = switchMode;
        this.finishAction = finishAction;
        this.stayTime = stayTime;
        this.durationTime = durationTime;
        this.startTime = startTime;
        this.repeatTime = repeatTime;
        this.enabled = enabled;
        this.createTime = createTime;
        this.hasSync = hasSync;
        this.hasDelete = hasDelete;
    }

    @Ignore
    public Task(int taskId, int taskType, String taskName, int cycleMode, int switchMode, int finishAction, long stayTime, long durationTime, String startTime, int repeatTime, boolean enabled, Date createTime) {
        this.tid = taskId;
        this.taskType = taskType;
        this.taskName = taskName;
        this.taskMode = cycleMode;
        this.switchMode = switchMode;
        this.finishAction = finishAction;
        this.stayTime = stayTime;
        this.durationTime = durationTime;
        this.startTime = startTime;
        this.repeatTime = repeatTime;
        this.enabled = enabled;
        this.createTime = createTime;
    }

    @Ignore
    public Task(long cloudId, int taskType, String taskName, int cycleMode, int switchMode, int finishAction, long stayTime, long durationTime, String startTime, int repeatTime, boolean enabled, Date createTime) {
        this.cloudId = cloudId;
        this.taskType = taskType;
        this.taskName = taskName;
        this.taskMode = cycleMode;
        this.switchMode = switchMode;
        this.finishAction = finishAction;
        this.stayTime = stayTime;
        this.durationTime = durationTime;
        this.startTime = startTime;
        this.repeatTime = repeatTime;
        this.enabled = enabled;
        this.createTime = createTime;
    }

    @Ignore
    public Task(long cloudId, int taskType, String taskName, int cycleMode, int switchMode, int finishAction, long stayTime, long durationTime, String startTime, int repeatTime, boolean enabled, Date createTime,String disinfectionFloors) {
        this.cloudId = cloudId;
        this.taskType = taskType;
        this.taskName = taskName;
        this.taskMode = cycleMode;
        this.switchMode = switchMode;
        this.finishAction = finishAction;
        this.stayTime = stayTime;
        this.durationTime = durationTime;
        this.startTime = startTime;
        this.repeatTime = repeatTime;
        this.enabled = enabled;
        this.createTime = createTime;
        this.disinfectionFloors = disinfectionFloors;
    }

    /**
     * 本地id
     */
    @PrimaryKey(autoGenerate = true)
    public long tid;

    /**
     * 云端id
     */
    @ColumnInfo(name = "t_cloud_id")
    public long cloudId = -1;

    /**
     * 任务类型：0 手动任务 1 定时任务
     */
    @ColumnInfo(name = "t_task_type")
    public int taskType;

    /**
     * 任务名称
     */
    @ColumnInfo(name = "t_task_name")
    public String taskName;

    /**
     * 循环模式 0 单次执行 1 时长循环
     */
    @ColumnInfo(name = "t_task_mode")
    public int taskMode;

    /**
     * 消毒开关 0 全程打开 1 目标点打开 2 全程关闭
     */
    @ColumnInfo(name = "t_switch_mode")
    public int switchMode;

    /**
     * 完成后的动作 0 充电 1 返回出发位置
     */
    @ColumnInfo(name = "t_finish_action")
    public int finishAction;

    /**
     * 停留时间（秒）
     */
    @ColumnInfo(name = "t_stay_time")
    public long stayTime;

    /**
     * 循环时长(秒）
     */
    @ColumnInfo(name = "t_duration_time")
    public long durationTime;

    /**
     * 开始时间
     */
    @ColumnInfo(name = "t_start_time")
    public String startTime;

    /**
     * 重复日期
     */
    @ColumnInfo(name = "t_repeat_time")
    public int repeatTime;

    /**
     * 是否启用
     */
    @ColumnInfo(name = "t_enabled")
    public boolean enabled;

    /**
     * 创建时间
     */
    @ColumnInfo(name = "t_create_time")
    public Date createTime;

    /**
     * 有无同步到云端
     */
    @ColumnInfo(name = "t_has_sync")
    public boolean hasSync = false;

    /**
     * 本地有无删除
     */
    @ColumnInfo(name = "t_has_delete")
    public boolean hasDelete = false;

    /**
     * 消毒楼层
     *
     * @return
     */
    @ColumnInfo(name = "t_disinfection_floors", defaultValue = "")
    public String disinfectionFloors;


    /**
     * 手动任务默认9点开始
     *
     * @return
     */
    public static Date defaultManualTaskStartTime() {
        Calendar instance = Calendar.getInstance(Locale.getDefault());
        instance.setTime(new Date());
        instance.set(Calendar.HOUR_OF_DAY, 9);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);
        return instance.getTime();
    }

    /**
     * 默认定时任务开始时间
     *
     * @return
     */
    public static String defaultScheduledTaskStartTime() {
        Calendar instance = Calendar.getInstance(Locale.getDefault());
        instance.set(Calendar.MINUTE, instance.get(Calendar.MINUTE) + 5);
        return new SimpleDateFormat( "HH:mm", Locale.getDefault()).format(instance.getTime());
    }

    /**
     * 默认定时任务的名称
     *
     * @param date
     * @return
     */
    public static String defaultScheduledTaskName(Context context, String date) {
        return context.getString(R.string.text_default_scheduled_task_name, date);
    }


    /**
     * 创建默认手动任务
     *
     * @return
     */
    public static Task defaultManualTask() {
        Date time = new Date();
        return new Task(
                -1,
                0,
                Constant.MANUAL_TASK_NAME,
                0,
                1,
                0,
                10,
                10 * 60,
                "",
                127,
                true,
                time
        );
    }

    /**
     * 创建默认定时任务
     *
     * @param taskName
     * @param startTime
     * @return
     */
    public static Task defaultScheduledTask(String taskName, String startTime) {
        Date time = new Date();
        return new Task(
                -1,
                1,
                taskName,
                0,
                1,
                0,
                10,
                10 * 60,
                startTime,
                127,
                true,
                time
        );
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("Task{");
        sb.append("tid=").append(tid);
        sb.append(", cloudId=").append(cloudId);
        sb.append(", taskType=").append(taskType);
        sb.append(", taskName='").append(taskName).append('\'');
        sb.append(", taskMode=").append(taskMode);
        sb.append(", switchMode=").append(switchMode);
        sb.append(", finishAction=").append(finishAction);
        sb.append(", stayTime=").append(stayTime);
        sb.append(", durationTime=").append(durationTime);
        sb.append(", startTime=").append(startTime);
        sb.append(", repeatTime=").append(repeatTime);
        sb.append(", enabled=").append(enabled);
        sb.append(", createTime=").append(createTime);
        sb.append(", hasSync=").append(hasSync);
        sb.append(", hasDelete=").append(hasDelete);
        sb.append(", disinfectionFloors='").append(disinfectionFloors).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
