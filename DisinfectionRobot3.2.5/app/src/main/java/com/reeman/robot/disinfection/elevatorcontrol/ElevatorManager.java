package com.reeman.robot.disinfection.elevatorcontrol;

import android.media.MediaPlayer;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.bjw.bean.ComBean;
import com.bjw.utils.SerialHelper;
import com.elvishew.xlog.XLog;
import com.reeman.robot.disinfection.R;
import com.reeman.robot.disinfection.controller.RobotActionController;
import com.reeman.robot.disinfection.utils.VoiceHelper;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.WebSocket;
import okhttp3.WebSocketListener;
import okio.ByteString;

import static com.reeman.robot.disinfection.base.BaseApplication.mApp;

public class ElevatorManager {

    private static ElevatorManager mInstance;

    private final Handler mHandler = new Handler(Looper.getMainLooper());

    private String webSocketUrl = "wss://tikong.kagobot.com/socket";

    private OkHttpClient mOkHttpClient;

    private WebSocket mWebSocket;

    private final SerialHelper serialHelper;

    private long lastOpeningTimeMills;

    private ElevatorStateCallback elevatorCallback;

    private Runnable lastSubmitRunnable;

    private boolean isOpen = false;

    private static final String CMD_FREE = "41542b52454c454153450d0a";
    private static final String CMD_OPEN_DOOR = "41542b4f50454e444f4f520d0a";
    private static final String CMD_CLOSE_DOOR = "41542b434c4f5345444f4f520d0a";
    private static final String CMD_REQUEST_PERMISSION = "41542b524551554553543d310d0a";


    private final Runnable isLocatingRunnable = new Runnable() {
        @Override
        public void run() {
            log("检查是否还在重定位===" + ElevatorState.isRelocating);
            if (ElevatorState.isRelocating) {
                mHandler.postDelayed(this, 200);
            } else {
                log("电梯内定位成功");
                VoiceHelper.play("voice_apply_map_success_leave", () -> {
                    elevatorCallback.onLeaveElevator();
                    RobotActionController.getInstance().setGlobalRadius(0.1);
                    RobotActionController.getInstance().navigationByPoint(ElevatorState.elevatorOut);
                });
                mHandler.removeCallbacks(this);
            }
        }
    };

    private final Runnable keepOpeningRunnable = new Runnable() {

        @Override
        public void run() {
            openDoor(true);
            mHandler.postDelayed(this, 7000);
        }
    };

    private Runnable permGrantedRunnable = new Runnable() {

        @Override
        public void run() {
            if (ElevatorState.isPermGranted) {
                ElevatorState.permGrantedTryCount = 0;
                return;
            }
            ElevatorState.permGrantedTryCount++;
            if (ElevatorState.permGrantedTryCount >= 10) {
                ElevatorState.permGrantedTryCount = 0;
                elevatorCallback.onPermissionRequestFailed();
            } else {
                requestPermissionBySerialPort();
                mHandler.postDelayed(this, 3000);
            }
        }
    };


    private ElevatorWebSocketListener socketListener;
    private Runnable webSocketTimeOutRunnable = new Runnable() {
        @Override
        public void run() {
            ElevatorState.isWebSocketConnectFailed = true;
            elevatorCallback.onWebSocketConnectFailed();
        }
    };

    public static ElevatorManager getInstance() {
        if (mInstance == null) {
            mInstance = new ElevatorManager();
        }
        return mInstance;
    }

    private ElevatorManager() {
        serialHelper = new SerialHelper() {
            @Override
            protected void onDataReceived(ComBean bean) {
                String beanStr = new String(bean.bRec, StandardCharsets.UTF_8);
                String data = beanStr.trim();
                log(data + "\n"
                        + "权限 :" + ElevatorState.isPermGranted
                        + ",是否在电梯中 :" + ElevatorState.isInsideElevator
                        + ",进电梯 :" + ElevatorState.isFromOut
                        + ",是否在重定位 :" + ElevatorState.isRelocating
                        + ",isOpen :" + isOpen);
                if (!isOpen) return;
                if (ElevatorState.loraID == null || ElevatorState.elevatorId == null) {
                    if (data.length() == 8) {
                        ElevatorState.loraID = data;
                    } else if (data.length() == 12) {
                        ElevatorState.elevatorId = data.substring(0, 8);
                        ElevatorState.supportedMaxFloor = Integer.valueOf(data.substring(10, 12), 16);
                    }
                    return;
                }
                if (data.startsWith("WAITING")) {
                    if (ElevatorState.isSendClosing) ElevatorState.isSendClosing = false;
                    if (ElevatorState.isPermGranted && ElevatorState.isInsideElevator) {
                        //收到电梯内关门回调
                        ElevatorState.isArrivalHandled = false;
                        //收到关门回调，移除关门延时任务
                        mHandler.removeCallbacks(lastSubmitRunnable);
                        //内呼目标楼层
                        lastSubmitRunnable = new Runnable() {
                            @Override
                            public void run() {
                                callElevator(ElevatorState.goalFloor);
                                mHandler.postDelayed(this, 5000);
                            }
                        };
                        mHandler.post(lastSubmitRunnable);
                    } else if (ElevatorState.isPermGranted && !ElevatorState.isFromOut) {
                        //出电梯到达电梯口，收到关门回调，释放权限
                        ElevatorState.isArrivalHandled = false;
                        //收到关门回调，移除关门延时任务
                        mHandler.removeCallbacks(lastSubmitRunnable);
                        free();
                    } else if (!ElevatorState.isPermGranted && !ElevatorState.isInsideElevator && ElevatorState.isFromOut) {
                        //请求权限成功
                        ElevatorState.isPermGranted = true;
                        elevatorCallback.onPermissionSuccess();
                        //外呼目标楼层
                        lastSubmitRunnable = new Runnable() {
                            @Override
                            public void run() {
                                callElevator(ElevatorState.nowFloor);
                                mHandler.postDelayed(this, 5000);
                            }
                        };
                        mHandler.post(lastSubmitRunnable);
                    }
                } else if (data.startsWith("GOING")) {
                    log("正在前往");
                    //移除回调
                    mHandler.removeCallbacks(lastSubmitRunnable);
                    //重新计时，从 GOING --> ARRIVED时间较长
                    mHandler.postDelayed(lastSubmitRunnable, 40_000);
                } else if (data.startsWith("ARRIVED")) {
                    log("是否响应过电梯到达 : " + ElevatorState.isArrivalHandled);
                    if (!ElevatorState.isArrivalHandled) {
                        mHandler.removeCallbacks(lastSubmitRunnable);
                        openDoor(false);
                        ElevatorState.isArrivalHandled = true;
                    }
                } else if (data.startsWith("OPENING")) {
                    if (ElevatorState.isSendClosing) return;
                    if (ElevatorState.isFirstOpeningTaskSubmit) {
                        mHandler.postDelayed(keepOpeningRunnable, 7000);
                        return;
                    }
                    if (ElevatorState.isInsideElevator && !ElevatorState.isFromOut) {
                        ElevatorState.isFirstOpeningTaskSubmit = true;
                        mHandler.post(isLocatingRunnable);
                        mHandler.postDelayed(keepOpeningRunnable, 7000);
                    } else if (ElevatorState.isFromOut && !ElevatorState.isInsideElevator) {
                        ElevatorState.isFirstOpeningTaskSubmit = true;
                        VoiceHelper.play("voice_mind_out", () -> {
                            elevatorCallback.onNaviIntoElevator();
                            RobotActionController.getInstance().setGlobalRadius(0.1);
                            RobotActionController.getInstance().navigationByPoint(ElevatorState.elevatorIn);
                        });

                        mHandler.postDelayed(keepOpeningRunnable, 7000);
                    }
                } else if (data.startsWith("FREE")) {
                    RobotActionController.getInstance().setGlobalRadius(0.225);
                    elevatorCallback.onPermissionRelease();
                    resetElevatorState();
                } else if (data.startsWith("BUSY")) {
                    mHandler.postDelayed(() -> requestPermissionBySerialPort(), 20_000);
                } else {
                    mHandler.removeCallbacks(lastSubmitRunnable);
                    mHandler.removeCallbacks(keepOpeningRunnable);
                    if (data.startsWith("DISORDER")) {
                        elevatorCallback.onError(mApp.getString(R.string.text_disorder, data));
                    } else if (data.startsWith("ERROR01")) {
                        elevatorCallback.onError(mApp.getString(R.string.text_error01, data, ElevatorState.nowFloor, ElevatorState.goalFloor));
                    } else if (data.startsWith("ERROR03")) {
                        //后台没有入库，没有权限
                        mHandler.removeCallbacks(permGrantedRunnable);
                        elevatorCallback.onPermissionRequestFailed();
                    } else if (data.startsWith("ERROR04")) {
                        elevatorCallback.onError(mApp.getString(R.string.text_error04, data));
                    } else if (data.startsWith("ERROR05")) {
                        elevatorCallback.onError(mApp.getString(R.string.text_error05, data));
                    } else if (data.startsWith("ERROR11")) {
                        elevatorCallback.onError(mApp.getString(R.string.text_error11, data));
                    } else if (data.startsWith("ERROR12")) {
                        elevatorCallback.onError(mApp.getString(R.string.text_error12, data));
                    } else if (data.startsWith("ERROR13")) {
                        elevatorCallback.onError(mApp.getString(R.string.text_error13, data));
                    } else if (data.startsWith("ERROR14")) {
                        elevatorCallback.onError(mApp.getString(R.string.text_error14, data));
                    }
                }
            }
        };
    }

    private void resendTask() {
        if (ElevatorState.isInsideElevator) {
            callElevator(ElevatorState.goalFloor);
            return;
        }
        callElevator(ElevatorState.nowFloor);
    }

    public void openPort(ElevatorStateCallback callback) {
        this.elevatorCallback = callback;
        try {
            serialHelper.setBaudRate("115200");
            String serialDevices = getSerialDevices();
            if (serialDevices == null) {
                throw new IllegalStateException(mApp.getString(R.string.text_serial_port_not_found));
            }
            log(serialDevices);
            serialHelper.setPort(serialDevices);
            serialHelper.open();
            //获取通信板id  AF000004
            isOpen = true;
            serialHelper.sendHex("41542b524f424f5449443f0d0a");
            mHandler.postDelayed(() -> {
                try {
                    //获取梯控id  AF0000040108
                    serialHelper.sendHex("41542b454c455641544f52313f0d0a");
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }, 100);
        } catch (Exception e) {
            elevatorCallback.onPortOpenFailed(e);
        }
    }

    public void closePort() {
        serialHelper.stopSend();
        serialHelper.close();
        isOpen = false;
    }

    public void onArriveElevatorIn() {
        RobotActionController.getInstance().applyMap(ElevatorState.goalFloor + "");
        elevatorCallback.onApplyingMap();
        mHandler.removeCallbacks(keepOpeningRunnable);
        ElevatorState.isRelocating = true;
        ElevatorState.isFromOut = false;
        ElevatorState.isInsideElevator = true;
        ElevatorState.isFirstOpeningTaskSubmit = false;
        lastSubmitRunnable = new Runnable() {
            @Override
            public void run() {
                closeDoor();
                mHandler.postDelayed(this, 10_000);
            }
        };
        mHandler.post(lastSubmitRunnable);
    }

    public void onArriveElevatorOut() {
        if (ElevatorState.isFromOut) {
            //进电梯 请求权限
            requestPermission();
        } else {
            //出电梯 关门
            log("出电梯，释放权限");
            ElevatorState.isInsideElevator = false;
            mHandler.removeCallbacks(keepOpeningRunnable);
            ElevatorState.isFirstOpeningTaskSubmit = false;
            lastSubmitRunnable = new Runnable() {
                @Override
                public void run() {
                    closeDoor();
                    mHandler.postDelayed(this, 10_000);
                }
            };
            mHandler.post(lastSubmitRunnable);
        }
    }

    private void requestPermission() {
        if (ElevatorState.highFloor) {
            log("webSocket申请电梯权限");
            connect();
        } else {
            log("申请电梯权限");
            requestPermissionBySerialPort();
            mHandler.postDelayed(permGrantedRunnable, 5000);
        }
    }

    public void onError() {
        ElevatorState.isErrorOccurred = true;
    }

    public interface ElevatorStateCallback {
        void onPermissionRelease();

        void onPortOpenFailed(Exception e);

        void onWebSocketAuthFailed(String errorMsg);

        void onPermissionRequestFailed();

        void onError(String data);

        void onWebSocketConnectFailed();

        void onWebSocketError(String msg);

        void onPermissionSuccess();

        void onNaviIntoElevator();

        void onApplyingMap();

        void onLeaveElevator();
    }

    private class ElevatorWebSocketListener extends WebSocketListener {

        private Map<Integer, String> map;
        private int failCount;

        ElevatorWebSocketListener() {
            map = new HashMap<>();
            map.put(0xdd, mApp.getString(R.string.error_dd));
            map.put(0xde, mApp.getString(R.string.error_de));
            map.put(0xdf, mApp.getString(R.string.error_df));
            map.put(-1, mApp.getString(R.string.error_parameter_error));
            map.put(-2, mApp.getString(R.string.error_request_error));
        }


        @Override
        public void onOpen(WebSocket webSocket, Response response) {
            mHandler.removeCallbacks(webSocketTimeOutRunnable);
            if (!ElevatorState.isWebSocketConnectFailed)
                authentication();
        }

        @Override
        public void onMessage(WebSocket webSocket, String text) {
            log(text);
            JSONObject result;
            try {
                result = new JSONObject(text);
            } catch (JSONException e) {
                return;
            }
            int msgType = result.optInt("msgType");
            int errorCode = result.optInt("errorCode");
            switch (msgType) {
                case -1:
                    //两分钟发送心跳包
                    mHandler.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            heartBeat();
                        }
                    }, 1000 * 60 * 2);
                    break;
                case 0:
                    //认证
                    if (errorCode == 0) {
                        log("认证成功,开始获取权限");
                        requestPermissionByWebSocket();
                        failCount = 0;
                    } else {
                        if (++failCount < 3) {
                            authentication();
                            return;
                        }
                        failCount = 0;
                        elevatorCallback.onWebSocketAuthFailed("梯控认证失败:" + result.optString("errorMsg"));

                    }
                    break;
                case 13:
                    //呼叫电梯
                    int status = result.optInt("status");
                    if (errorCode == 0) {
                        failCount = 0;
                        switch (status) {
                            case 0xd0:
                                break;
                            case 0xd1:
                                log("权限申请成功");
                                heartBeat();
                                ElevatorState.isPermGranted = true;
                                callElevator(ElevatorState.nowFloor);
                                break;
                            case 0xd2:
                                //呼梯成功
                                log("呼梯成功 going");
                                disconnect();
                                break;
                            case 0xd3:
                                //电梯到达目标楼层
                                log("电梯到达 arrived");
                                openDoor(false);
                                disconnect();
                                break;
                            case 0xdd:
                            case 0xde:
                            case 0xdf:
                                String msg = map.get(status);
                                elevatorCallback.onWebSocketError("状态异常, status: " + status + " msg: " + msg);
                                break;
                        }
                    } else {
                        if (++failCount < 3) {
                            if (ElevatorState.isPermGranted) {
                                callElevator(ElevatorState.nowFloor);
                            } else {
                                requestPermissionByWebSocket();
                            }
                            return;
                        }
                        failCount = 0;
                        log(errorCode + " ");
                        elevatorCallback.onWebSocketError("梯控云服务器请求异常, errorCode: " + errorCode + " msg: " + map.get(errorCode));
                    }
                    break;
            }
        }

        @Override
        public void onMessage(WebSocket webSocket, ByteString bytes) {
            super.onMessage(webSocket, bytes);
            log("onMessage");
        }

        @Override
        public void onClosing(WebSocket webSocket, int code, String reason) {
            super.onClosing(webSocket, code, reason);
            log("onClosing");
        }

        @Override
        public void onClosed(WebSocket webSocket, int code, String reason) {
            super.onClosed(webSocket, code, reason);
            log("onClosed");
        }

        @Override
        public void onFailure(WebSocket webSocket, Throwable t, Response response) {
            super.onFailure(webSocket, t, response);
            log("onFailure");
        }

        public void heartBeat() {
            String jsonMsg = "{\"msgType\":-1}";
            mWebSocket.send(jsonMsg);
        }

        public void requestPermissionByWebSocket() {
            try {
                long timeMillis = System.currentTimeMillis();
                JSONObject jo = new JSONObject();
                jo.put("msgType", 13);
                jo.put("cmdId", 1);
                jo.put("elevatorId", ElevatorState.elevatorId);
                jo.put("robotId", ElevatorState.loraID);
                jo.put("seq", String.valueOf(timeMillis));
                mWebSocket.send(jo.toString());
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }

        public void callElevator(int floor) {
            try {
                long timeMillis = System.currentTimeMillis();
                JSONObject jo = new JSONObject();
                jo.put("msgType", 13);
                jo.put("cmdId", 2);
                jo.put("elevatorId", ElevatorState.elevatorId);
                jo.put("robotId", ElevatorState.loraID);
                jo.put("floorDst", floor);
                jo.put("seq", String.valueOf(timeMillis));
                mWebSocket.send(jo.toString());
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }

        public void authentication() {
            String jsonMsg = "{\"msgType\":0,\"userId\":\"锐曼机器人\",\"password\":\"ruimanrobot\"}";
            mWebSocket.send(jsonMsg);
        }
    }

    public void connect() {
        if (mOkHttpClient == null) {
            mOkHttpClient = new OkHttpClient.Builder()
                    .readTimeout(5, TimeUnit.SECONDS)//设置读取超时时间
                    .writeTimeout(5, TimeUnit.SECONDS)//设置写的超时时间
                    .connectTimeout(5, TimeUnit.SECONDS)//设置连接超时时间
                    .build();
            socketListener = new ElevatorWebSocketListener();
        }
        ElevatorState.isWebSocketConnectFailed = false;
        Request request = new Request.Builder().url(webSocketUrl).build();
        mWebSocket = mOkHttpClient.newWebSocket(request, socketListener);
        mHandler.postDelayed(webSocketTimeOutRunnable, 8_000);
    }

    public void disconnect() {
        log("断开WebSocket连接");
        mWebSocket.cancel();
    }

    public void openDoor(boolean isKeepOpening) {
        if (System.currentTimeMillis() - lastOpeningTimeMills < 3000) return;
        lastOpeningTimeMills = System.currentTimeMillis();
        if (isKeepOpening) {
            log("保持开门");
        } else {
            log("打开电梯门");
        }
        sendCmd(CMD_OPEN_DOOR);
    }

    public void closeDoor() {
        log("关闭电梯门");
        ElevatorState.isSendClosing = true;
        sendCmd(CMD_CLOSE_DOOR);
    }

    public void requestPermissionBySerialPort() {
        log("请求权限");
        sendCmd(CMD_REQUEST_PERMISSION);
    }

    public void free() {
        log("释放权限");
        sendCmd(CMD_FREE);
    }

    public void sendCmd(String cmd) {
        try {
            log(cmd);
            serialHelper.sendHex(cmd);
        } catch (Exception e) {
            elevatorCallback.onError(mApp.getString(R.string.text_elevator_io_error, e.getMessage()));
        }
    }

    public void resetElevatorState() {
        ElevatorState.isFromOut = true;
        ElevatorState.isPermGranted = false;
        ElevatorState.isInsideElevator = false;
        ElevatorState.isRelocating = false;
        ElevatorState.isErrorOccurred = false;
        ElevatorState.isFirstOpeningTaskSubmit = false;
        ElevatorState.isWebSocketConnectFailed = false;
        ElevatorState.permGrantedTryCount = 0;
        ElevatorState.isArrivalHandled = false;
        mHandler.removeCallbacks(keepOpeningRunnable);
        mHandler.removeCallbacks(lastSubmitRunnable);
    }

    public void callElevator(int floor) {
        log("呼叫电梯到" + floor + "层");
        String order = "AT+PUSHBTN=" + (floor <= 15 ? "0" : "") + Integer.toHexString(floor);
        String orderHex = stringToHexString(order) + "0d0a";
        sendCmd(orderHex);
    }

    public static String stringToHexString(String s) {
        StringBuilder str = new StringBuilder();
        for (int i = 0; i < s.length(); i++) {
            int ch = s.charAt(i);
            String s4 = Integer.toHexString(ch);
            str.append(s4);
        }
        return str.toString();
    }

    public String getSerialDevices() {
        String path;
        File portFile = new File("/dev");
        File[] files = portFile.listFiles();
        for (File file : files) {
            if (file.getAbsolutePath().startsWith("/dev/ttyUSB")) {
                path = file.getPath();
                return path;
            }
        }
        return null;
    }

    private void log(Object msg) {
        XLog.tag(ElevatorManager.class.getSimpleName()).e(msg);
    }
}
