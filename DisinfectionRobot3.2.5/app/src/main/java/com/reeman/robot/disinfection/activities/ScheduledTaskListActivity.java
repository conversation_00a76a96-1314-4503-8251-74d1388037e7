package com.reeman.robot.disinfection.activities;

import android.app.Dialog;
import android.view.View;
import android.widget.ImageButton;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.reeman.robot.disinfection.R;
import com.reeman.robot.disinfection.adapters.ScheduledTaskListItemAdapter;
import com.reeman.robot.disinfection.base.BaseActivity;
import com.reeman.robot.disinfection.contract.ScheduledTaskListContract;
import com.reeman.robot.disinfection.event.Event;
import com.reeman.robot.disinfection.presenter.impl.ScheduledTaskListPresenter;
import com.reeman.robot.disinfection.repository.entities.Task;
import com.reeman.robot.disinfection.utils.ScreenUtils;
import com.reeman.robot.disinfection.utils.ToastUtils;
import com.reeman.robot.disinfection.widgets.EasyDialog;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.List;


public class ScheduledTaskListActivity extends BaseActivity implements View.OnClickListener,
        ScheduledTaskListContract.View, ScheduledTaskListItemAdapter.OnTaskListItemClickListener {


    private ScheduledTaskListItemAdapter adapter;
    private ScheduledTaskListPresenter presenter;

    @Override
    protected boolean shouldResponse2TimeEvent() {
        return true;
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        if (hasFocus) {
            ScreenUtils.hideBottomUIMenu(this);
        }
    }

    @Override
    protected int getLayoutRes() {
        return R.layout.activity_scheduled_task_list;
    }

    @Override
    protected void initView() {
        TextView tvBack = $(R.id.tv_back);
        tvBack.setOnClickListener(this);

        RecyclerView rvTaskList = $(R.id.rv_task_list);
        adapter = new ScheduledTaskListItemAdapter();
        adapter.setListItemClickListener(this);
        rvTaskList.setAdapter(adapter);
        rvTaskList.setLayoutManager(new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false));

        ImageButton fabNewTask = $(R.id.ib_new_task);
        fabNewTask.setOnClickListener(this);
    }

    @Override
    protected void initData() {
        presenter = new ScheduledTaskListPresenter(this);
    }

    @Override
    protected void onResume() {
        super.onResume();
        //加载列表
        presenter.getAllScheduledTaskList();
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        switch (id) {
            case R.id.tv_back:
                finish();
                break;
            case R.id.ib_new_task:
                String startTime = Task.defaultScheduledTaskStartTime();
                BaseActivity.start(
                        this,
                        TaskCreateActivity.class,
                        Task.defaultScheduledTask(Task.defaultScheduledTaskName(this, startTime), startTime));
                break;
        }
    }

    /**
     * 列表加载完成
     *
     * @param tasks
     */
    @Override
    public void onScheduledTaskListLoaded(List<Task> tasks) {
        adapter.setTaskList(tasks);
    }

    /**
     * 列表加载失败
     *
     * @param error
     */
    @Override
    public void onScheduledTaskListLoadFailed(String error) {
        ToastUtils.showShortToast(error);
    }

    /**
     * 列表项被点击
     *
     * @param task
     */
    @Override
    public void onTaskListItemClick(Task task) {
        BaseActivity.start(this, TaskCreateActivity.class, task);
    }

    /**
     * 列表项被长按
     */
    @Override
    public void onTaskListItemLongClick(int index, Task task) {
        EasyDialog.getInstance(this).confirm(getString(R.string.text_confirm_delete_this_task), new EasyDialog.OnViewClickListener() {
            @Override
            public void onViewClick(Dialog dialog, int id) {
                dialog.dismiss();
                if (id == R.id.btn_confirm) {
                    presenter.deleteTask(index, task);
                }
            }
        });
    }

    /**
     * 任务禁用启用状态发生改变
     *
     * @param position
     * @param task
     * @param enable
     */
    @Override
    public void onTaskEnabledStateChange(int position, Task task, boolean enable) {
        presenter.updateTaskEnableState(position, task, enable);
    }

    /**
     * 定时任务启用成功
     *
     * @param position
     * @param enable
     */
    @Override
    public void onTaskEnableStateUpdateSuccess(int position, boolean enable) {
        runOnUiThread(() -> ToastUtils.showShortToast(enable ? getString(R.string.text_task_enabled) : getString(R.string.text_task_disabled)));
    }

    /**
     * 定时任务启用失败
     *
     * @param message
     */
    @Override
    public void onTaskEnableStateUpdateFailed(String message) {
        ToastUtils.showShortToast(message);
    }

    /**
     * @param index
     */
    @Override
    public void onTaskDeleted(int index) {
        runOnUiThread(() -> adapter.removeTask(index));
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onTaskUpdate(Event.OnTaskUpdateEvent event) {
        adapter.setTaskList(event.list);
        //ToastUtils.showShortToast(getString(R.string.text_task_already_update));
    }
}