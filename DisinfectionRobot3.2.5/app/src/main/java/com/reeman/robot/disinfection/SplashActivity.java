package com.reeman.robot.disinfection;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.media.AudioManager;
import android.view.animation.LinearInterpolator;
import android.widget.ImageView;

import com.reeman.robot.disinfection.activities.GuideActivity;
import com.reeman.robot.disinfection.activities.LanguageChooseActivity;
import com.reeman.robot.disinfection.activities.MainActivity;
import com.reeman.robot.disinfection.base.BaseActivity;
import com.reeman.robot.disinfection.constants.BuglyConstants;
import com.reeman.robot.disinfection.constants.Constant;
import com.reeman.robot.disinfection.elevatorcontrol.ElevatorState;
import com.reeman.robot.disinfection.utils.LocaleUtil;
import com.reeman.robot.disinfection.utils.SpManager;

import static com.reeman.robot.disinfection.base.BaseApplication.mApp;

public class SplashActivity extends BaseActivity {

    private ImageView ivLoading;

    @Override
    protected boolean disableBottomNavigationBar() {
        return false;
    }

    @Override
    protected int getLayoutRes() {
        return R.layout.activity_splash;
    }

    @Override
    protected void initView() {
        ivLoading = $(R.id.iv_loading);
    }

    @Override
    protected void initData() {
        int languageType = SpManager.getInstance().getInt(Constant.KEY_LANGUAGE_TYPE, Constant.DEFAULT_LANGUAGE_TYPE);
        if (languageType != -1 && languageType != LocaleUtil.getLocaleType()) {
            LocaleUtil.changeAppLanguage(getResources(), languageType);
            LocaleUtil.changeAppLanguage(mApp.getResources(), languageType);
            BuglyConstants.updateBuglyStrings(getResources());
        }

        int volume = SpManager.getInstance().getInt(Constant.SYS_VOLUME, Constant.DEFAULT_MEDIA_VOLUME);
        AudioManager audioManager = (AudioManager) getSystemService(Context.AUDIO_SERVICE);
        audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, volume, 0);
    }

    @Override
    protected void onResume() {
        super.onResume();
        startLoadingAnimation();
        ElevatorState.highFloor = SpManager.decodeBool(Constant.KEY_IS_HIGH_FLOOR,false);
        ElevatorState.elevatorIn = getString(R.string.elevator_in_point);
        ElevatorState.elevatorOut = getString(R.string.elevator_out_point);
    }

    private void startLoadingAnimation() {
        ObjectAnimator animator = ObjectAnimator.ofFloat(ivLoading, "rotation", 0, 360);
        animator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                onSplashAnimationEnd();
            }
        });
        animator.setDuration(1000);
        animator.setRepeatCount(3);
        animator.setInterpolator(new LinearInterpolator());
        animator.setRepeatMode(ValueAnimator.RESTART);
        animator.start();
    }

    private void onSplashAnimationEnd() {
        //如果没有引导过选择语言，跳转语言选择界面
        if (!SpManager.getInstance().getBoolean(Constant.IS_LANGUAGE_CHOSEN, Constant.DEFAULT_LANGUAGE_CHOSEN)) {
            LanguageChooseActivity.startActivity(this, getClass().getSimpleName());
            finish();
            return;
        }

        //如果引导过选择语言，没走完引导流程，则跳转引导界面
        if (SpManager.getInstance().getInt(Constant.CURRENT_GUIDE_STEP, Constant.DEFAULT_GUIDE_STEP) < Constant.GUIDE_COUNT) {
            GuideActivity.startActivity(this, getClass().getSimpleName());
            finish();
            return;
        }

        //语言选择过， 引导完成，跳转主界面
        BaseActivity.start(this, MainActivity.class, null);
        finish();
    }
}