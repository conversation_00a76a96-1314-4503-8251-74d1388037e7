package com.reeman.robot.disinfection.constants;


public class Constant {
    public static final String SP_NAME = "disinfection_robot";
    public static final String MANUAL_TASK_NAME = "手动任务";
    public static final String EXTRA = "extra";
    public static final String LOCK_SCREEN = "lock_screen";
    public static final boolean DEFAULT_LOCK_SCREEN = false;
    public static final String VOICE_BROADCAST = "voice_broadcast";
    public static final boolean DEFAULT_VOICE_BROADCAST = true;
    public static final String NAV_SPEED = "nav_speed";
    public static final String DEFAULT_NAV_SPEED = "0.4";
    public static final String LOW_POWER = "low_power";
    public static final int DEFAULT_LOW_POWER = 20;
    public static final String SYS_VOLUME = "sys_volume";
    public static final int DEFAULT_MEDIA_VOLUME = 12;
    public static final String DELAY_TIME = "delay_time";
    public static final int DEFAULT_DELAY_TIME = 10;
    public static final String LOCK_SCREEN_PASSWORD = "lock_screen_password";
    public static final String NAV_MODE = "nav_mode";
    public static final String HAS_GUIDE_BUILD_MAP = "has_guide_build_map";
    public static final int DEFAULT_GUIDE_STEP = 0;
    public static final String CURRENT_GUIDE_STEP = "current_guide_step";
    public static final String IS_LANGUAGE_CHOSEN = "is_language_chosen";
    public static final boolean DEFAULT_LANGUAGE_CHOSEN = false;
    public static final String KEY_LANGUAGE_TYPE = "key_language_type";
    public static final int DEFAULT_LANGUAGE_TYPE = -1;
    public static final String EXTRA_INT = "extra_int";
    public static final String ROBOT_TYPE = "robot_type";
    public static final String USERNAME = "username";
    public static final String PASSWORD = "password";
    public static final String ACCESS_TOKEN = "accessToken";
    public static final String REFRESH_TOKEN = "refreshToken";
    public static final String AUTO_LOGIN = "auto_login";
    public static final String CURRENT_GUIDE_OPERATION_STEP = "has_guide_operation";
    public static final String DEFAULT_USERNAME = "";
    public static final String DEFAULT_PASSWORD = "";
    public static final int GUIDE_COUNT = 4;
    public static final int RESPONSE_TIME = 15;
    public static final String DISINFECTION_PROMPT = "disinfection_prompt";
    public static final String KEY_GATING_SERIAL_PORT = "key_gating_serial_port";
    public static final String KEY_DEFAULT_GATING_SERIAL_PORT = "/dev/ttyUSB0";
    public static final String KEY_ELEVATOR_SERIAL_PORT = "key_elevator_serial_port";
    public static final String KEY_DEFAULT_ELEVATOR_SERIAL_PORT = "/dev/ttyUSB1";
    public static final String KEY_GATING_SWITCH = "key_gating_switch";
    public static final boolean DEFAULT_GATING_SWITCH= false;
    public static final String KEY_ELEVATOR_SWITCH = "key_elevator_switch";
    public static final boolean DEFAULT_ELEVATOR_SWITCH= true;
    public static final String TASK_RESULT_TEXT = "TASK_RESULT_TEXT";
    public static final String TASK_RESULT_VOICE = "TASK_RESULT_VOICE";
    public static final String KEY_IS_HIGH_FLOOR = "KEY_IS_HIGH_FLOOR";
    public static long lastUpdateTimeMills;
    public static final String DEFAULT_MAP = "default_map";
    public static final String LOCAL_MAPS = "local_maps";
    public static final String ELEVATOR_WIFI_NAME = "elevator_wifi_name";
    public static final String ELEVATOR_WIFI_PWD = "elevator_wifi_pwd";
    public static final String WIFI_CAPABILITIES = "wifi_capabilities";
    public static final String PORT_MAPPING ="PORT_MAPPING";

    public static final String SP_MAIN_IP = "SP_MAIN_IP";
    public static final String DEFAULT_MAIN_IP = "127.0.0.1:3964";
    public static final String SP_OUT_IP = "SP_OUT_IP";
    public static final String DEFAULT_OUT_IP = "127.0.0.1:3964";
}
