package com.reeman.robot.disinfection.adapters;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.SwitchCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.reeman.robot.disinfection.R;
import com.reeman.robot.disinfection.repository.entities.Task;

import java.util.List;


public class ScheduledTaskListItemAdapter extends RecyclerView.Adapter<ScheduledTaskListItemAdapter.ViewHolder> {

    private List<Task> taskList;

    public void setTaskList(List<Task> tasks) {
        this.taskList = tasks;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View root = LayoutInflater.from(parent.getContext()).inflate(R.layout.layout_scheduled_task_list_item, parent, false);
        return new ViewHolder(root);
    }

    @Override
    public void onBindViewHolder(@NonNull ScheduledTaskListItemAdapter.ViewHolder holder, int position) {
        Task task = taskList.get(position);
        holder.tvTaskName.setText(task.taskName);
        holder.itemView.setOnClickListener((v) -> {
            if (listItemClickListener != null) {
                listItemClickListener.onTaskListItemClick(taskList.get(position));
            }
        });
        holder.itemView.setOnLongClickListener((v) -> {
            if (listItemClickListener != null) {
                listItemClickListener.onTaskListItemLongClick(holder.getAdapterPosition(), task);
            }
            return true;
        });
        holder.tvStartTime.setText(task.startTime);
        holder.swTaskEnabled.setChecked(task.enabled);
        holder.swTaskEnabled.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (listItemClickListener != null) {
                    listItemClickListener.onTaskEnabledStateChange(position, taskList.get(position), holder.swTaskEnabled.isChecked());
                }
            }
        });
    }

    @Override
    public int getItemCount() {
        return taskList == null ? 0 : taskList.size();
    }

    private OnTaskListItemClickListener listItemClickListener;

    public void setListItemClickListener(OnTaskListItemClickListener listItemClickListener) {
        this.listItemClickListener = listItemClickListener;
    }

    public void removeTask(int index) {
        taskList.remove(index);
        notifyItemRemoved(index);
    }

    public interface OnTaskListItemClickListener {
        void onTaskListItemClick(Task task);

        void onTaskListItemLongClick(int index, Task task);

        void onTaskEnabledStateChange(int position, Task task, boolean enable);
    }


    public static class ViewHolder extends RecyclerView.ViewHolder {
        private final TextView tvTaskName;
        private final TextView tvStartTime;
        private final SwitchCompat swTaskEnabled;
        private final View itemView;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            this.itemView = itemView;
            tvTaskName = itemView.findViewById(R.id.tv_task_name);
            tvStartTime = itemView.findViewById(R.id.tv_task_start_time);
            swTaskEnabled = itemView.findViewById(R.id.sw_task_enable);
        }
    }
}
