package com.reeman.robot.disinfection.activities;


import android.content.Intent;
import android.content.SharedPreferences;
import android.view.View;
import android.widget.EditText;
import android.widget.RadioGroup;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.reeman.robot.disinfection.R;
import com.reeman.robot.disinfection.base.BaseActivity;
import com.reeman.robot.disinfection.constants.Constant;
import com.reeman.robot.disinfection.contract.ElevatorSettingContract;
import com.reeman.robot.disinfection.controller.ElevatorController;
import com.reeman.robot.disinfection.elevatorcontrol.ElevatorState;
import com.reeman.robot.disinfection.event.Event;
import com.reeman.robot.disinfection.presenter.impl.ElevatorSettingPresenter;
import com.reeman.robot.disinfection.request.model.MapListResponse;
import com.reeman.robot.disinfection.utils.ScreenUtils;
import com.reeman.robot.disinfection.utils.SoftKeyboardStateWatcher;
import com.reeman.robot.disinfection.utils.SpManager;
import com.reeman.robot.disinfection.utils.ToastUtils;
import com.reeman.robot.disinfection.utils.WIFIUtils;
import com.reeman.robot.disinfection.widgets.EasyDialog;
import com.reeman.robot.disinfection.widgets.EasyTextWatcher;
import com.reeman.robot.disinfection.widgets.MapChooseDialog;

import java.util.ArrayList;
import java.util.List;
import java.util.StringTokenizer;


public class ElevatorSettingActivity extends BaseActivity implements View.OnClickListener, ElevatorSettingContract.View, SoftKeyboardStateWatcher.SoftKeyboardStateListener, MapChooseDialog.OnMapListItemSelectedListener {

    private ElevatorSettingPresenter presenter;

    private EditText etMainIP1, etMainIP2, etMainIP3, etMainIP4, etMainPort,
            etOutIP1, etOutIP2, etOutIP3, etOutIP4, etOutPort;

    private TextView tvDefaultMap, tvWifiName;

    private RadioGroup rgFloorLevel;

    private SoftKeyboardStateWatcher softKeyboardStateWatcher;

    private MapChooseDialog mapChooseDialog;

    private int current;

    private String defaultMap, defaultWifi;


    @Override
    protected int getLayoutRes() {
        return R.layout.activity_elevator_setting;
    }

    @Override
    protected void initView() {
        tvWifiName = $(R.id.tv_wifi_name);
        tvDefaultMap = $(R.id.tv_default_map);
        etMainIP1 = $(R.id.et_main_ip1);
        etMainIP2 = $(R.id.et_main_ip2);
        etMainIP3 = $(R.id.et_main_ip3);
        etMainIP4 = $(R.id.et_main_ip4);
        etOutIP1 = $(R.id.et_out_ip1);
        etOutIP2 = $(R.id.et_out_ip2);
        etOutIP3 = $(R.id.et_out_ip3);
        etOutIP4 = $(R.id.et_out_ip4);
        etMainPort = $(R.id.et_main_port);
        etOutPort = $(R.id.et_out_port);
        rgFloorLevel = $(R.id.rg_floor_level);
        $(R.id.btn_wifi_choose).setOnClickListener(this);
        $(R.id.btn_port_choose).setOnClickListener(this);
        $(R.id.btn_choose).setOnClickListener(this);
        $(R.id.tv_back).setOnClickListener(this);

    }

    @Override
    protected void initData() {
        SharedPreferences sp = SpManager.getInstance();
        softKeyboardStateWatcher = new SoftKeyboardStateWatcher(getWindow().getDecorView());
        softKeyboardStateWatcher.addSoftKeyboardStateListener(this);
        presenter = new ElevatorSettingPresenter(this);
        defaultMap = sp.getString(Constant.DEFAULT_MAP, "");
        defaultWifi = sp.getString(Constant.ELEVATOR_WIFI_NAME, "");
        sp.edit().putBoolean(Constant.HAS_GUIDE_BUILD_MAP, true).apply();
        tvWifiName.setText(defaultWifi.equals("") ? WIFIUtils.getConnectWifiSSID(this) : defaultWifi);
        String localMaps = sp.getString(Constant.LOCAL_MAPS, "");
        StringTokenizer stringTokenizer = new StringTokenizer(localMaps, "\n");
        ArrayList<String> maps = new ArrayList<>();
        while (stringTokenizer.hasMoreElements()) {
            maps.add(stringTokenizer.nextToken());
        }
        for (String map : maps) {
            try {
                Integer.parseInt(map);
            } catch (NumberFormatException e) {
                e.printStackTrace();
                ToastUtils.showShortToast(getString(R.string.text_map_name_authorized));
            }
        }
        if (!defaultMap.equals("")) {
            tvDefaultMap.setText(defaultMap);
        }
//        initIPAndPort();
    }


    @Override
    protected void onDestroy() {
        super.onDestroy();
        softKeyboardStateWatcher.removeSoftKeyboardStateListener(this);
    }

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.btn_wifi_choose:
                BaseActivity.startForResult(this, WiFiConnectActivity.class, getClass().getSimpleName());
                break;
            case R.id.btn_choose:
                presenter.onChooseMap(this, Event.getIpEvent().ipAddress);
                break;
            case R.id.tv_back:
                ElevatorState.highFloor = rgFloorLevel.getCheckedRadioButtonId() == R.id.rb_floor_level_high;
                SpManager.encode(Constant.KEY_IS_HIGH_FLOOR,ElevatorState.highFloor);
//                String mainIP = etMainIP1.getText().toString() + "." + etMainIP2.getText().toString() + "." + etMainIP3.getText().toString() + "." + etMainIP4.getText().toString();
//                String outIP = etOutIP1.getText().toString() + "." + etOutIP2.getText().toString() + "." + etOutIP3.getText().toString() + "." + etOutIP4.getText().toString();
//                if (!IPUtils.ipCheck(mainIP)){
//                    etMainIP1.setText("");
//                    etMainIP2.setText("");
//                    etMainIP3.setText("");
//                    etMainIP4.setText("");
//                    ToastUtils.showShortToast(getString(R.string.text_write_right_ip));
//                    return;
//                }
//                if (!IPUtils.ipCheck(outIP)){
//                    etOutIP1.setText("");
//                    etOutIP2.setText("");
//                    etOutIP3.setText("");
//                    etOutIP4.setText("");
//                    ToastUtils.showShortToast(getString(R.string.text_write_right_ip));
//                    return;
//                }
//                mainIP = mainIP+":"+etMainPort.getText().toString();
//                outIP = outIP+":"+etOutPort.getText().toString();
//                ElevatorController.getInstance().setMainIP(mainIP);
//                ElevatorController.getInstance().setOutIP(outIP);
                finish();
                break;
            case R.id.btn_port_choose:
                start(this, PortMappingActivity.class);
                break;
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        String connectWifiSSID = WIFIUtils.getConnectWifiSSID(this);
        tvWifiName.setText(connectWifiSSID);
        SpManager.getInstance().edit().putString(Constant.ELEVATOR_WIFI_NAME, connectWifiSSID).apply();
    }

    @Override
    public void onMapListLoaded(List<MapListResponse.Map> mapList) {
        String map = SpManager.getInstance().getString(Constant.DEFAULT_MAP, "");
        boolean authorized = true;//检查地图名称是否合法
        MapListResponse.Map currentMap;
        for (int i = 0; i < mapList.size(); i++) {
            currentMap = mapList.get(i);
            try {
                Integer.parseInt(currentMap.name);
            } catch (Exception e) {
                e.printStackTrace();
                authorized = false;
            }
            if (currentMap.name.equals(map)) {
                currentMap.selected = true;
                current = i;
            }
        }
        if (!authorized) {
            EasyDialog.getInstance(this).warn(getString(R.string.text_map_name_authorized), (dialog, id) -> dialog.dismiss());
            return;
        }
        mapChooseDialog = new MapChooseDialog(this, getString(R.string.text_choose_floor), mapList, current, this);
        mapChooseDialog.show();
    }

    @Override
    public void onMapListLoadedFailed(Throwable throwable) {
        ToastUtils.showShortToast(getString(R.string.text_map_load_failed));
    }

    @Override
    public void onSoftKeyboardOpened(int keyboardHeightInPx) {

    }

    @Override
    public void onSoftKeyboardClosed() {
        ScreenUtils.hideBottomUIMenu(this);
    }

    @Override
    public void onMapListItemSelected(MapChooseDialog mapChooseDialog, String map) {
        mapChooseDialog.dismiss();
        tvDefaultMap.setText(map);
        SpManager.getInstance().edit().putString(Constant.DEFAULT_MAP, map).apply();
    }

    @Override
    public void onNoMapSelected() {

    }

    private void initIPAndPort() {
        String mainIP = ElevatorController.getInstance().getMainIP();
        String[] splitMainIPAndPort = mainIP.split(":");
        String[] splitMainIP = splitMainIPAndPort[0].split("\\.");
        String outIP = ElevatorController.getInstance().getOutIP();
        String[] splitOutIPAndPort = outIP.split(":");
        String[] splitOutIP = splitOutIPAndPort[0].split("\\.");
        etMainIP1.setText(splitMainIP[0]);
        etMainIP2.setText(splitMainIP[1]);
        etMainIP3.setText(splitMainIP[2]);
        etMainIP4.setText(splitMainIP[3]);
        etMainPort.setText(splitMainIPAndPort[1]);
        etOutIP1.setText(splitOutIP[0]);
        etOutIP2.setText(splitOutIP[1]);
        etOutIP3.setText(splitOutIP[2]);
        etOutIP4.setText(splitOutIP[3]);
        etOutPort.setText(splitOutIPAndPort[1]);
        etMainIP1.addTextChangedListener(new EasyTextWatcher() {
            @Override
            public void onTextChanged(CharSequence charSequence) {
                if (charSequence.length() == 3) etMainIP2.requestFocus();
            }

        });
        etMainIP2.addTextChangedListener(new EasyTextWatcher() {
            @Override
            public void onTextChanged(CharSequence charSequence) {
                if (charSequence.length() == 3) etMainIP3.requestFocus();
            }
        });
        etMainIP3.addTextChangedListener(new EasyTextWatcher() {
            @Override
            public void onTextChanged(CharSequence charSequence) {
                if (charSequence.length() == 3) etMainIP4.requestFocus();
            }
        });
        etMainIP4.addTextChangedListener(new EasyTextWatcher() {
            @Override
            public void onTextChanged(CharSequence charSequence) {
                if (charSequence.length() == 3) etMainPort.requestFocus();
            }
        });
        etOutIP1.addTextChangedListener(new EasyTextWatcher() {
            @Override
            public void onTextChanged(CharSequence charSequence) {
                if (charSequence.length() == 3) etOutIP2.requestFocus();
            }

        });
        etOutIP2.addTextChangedListener(new EasyTextWatcher() {
            @Override
            public void onTextChanged(CharSequence charSequence) {
                if (charSequence.length() == 3) etOutIP3.requestFocus();
            }
        });
        etOutIP3.addTextChangedListener(new EasyTextWatcher() {
            @Override
            public void onTextChanged(CharSequence charSequence) {
                if (charSequence.length() == 3) etOutIP4.requestFocus();
            }
        });
        etOutIP4.addTextChangedListener(new EasyTextWatcher() {
            @Override
            public void onTextChanged(CharSequence charSequence) {
                if (charSequence.length() == 3) etOutPort.requestFocus();
            }
        });
    }
}