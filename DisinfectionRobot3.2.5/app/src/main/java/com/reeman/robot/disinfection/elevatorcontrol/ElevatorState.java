package com.reeman.robot.disinfection.elevatorcontrol;

public class ElevatorState {

    public static boolean isSendClosing = false;

    //true是进电梯 false 出电梯
    public static boolean isFromOut = true;

    //权限是否授予
    public static boolean isPermGranted = false;

    //当前是否在电梯中
    public static boolean isInsideElevator = false;

    //电梯口
    public static String elevatorOut = "电梯口";

    //电梯
    public static String elevatorIn = "电梯内";

    //机器人通信版Id
    public static String loraID;

    //梯控id
    public static String elevatorId;

    //目标楼层
    public static int goalFloor = 0;

    //当前楼层
    public static int nowFloor = 0;

    //是否高于七层
    public static boolean highFloor = false;

    //是否在重定位当中
    public static boolean isRelocating = false;

    //尝试获取权限次数
    public static int permGrantedTryCount = 0;

    //最大支持的楼层
    public static int supportedMaxFloor = -1;

    //出错了
    public static boolean isErrorOccurred = false;

    //WebSocket连接失败
    public static boolean isWebSocketConnectFailed = false;

    //防止多次保持开门导致重复发送导航指令
    public static boolean isFirstOpeningTaskSubmit = false;

    //是否响应过电梯到达，解决回调多次Arrived造成多次开门的问题
    public static boolean isArrivalHandled = false;
}
