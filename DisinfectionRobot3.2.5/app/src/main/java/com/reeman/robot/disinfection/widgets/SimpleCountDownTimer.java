package com.reeman.robot.disinfection.widgets;

import android.os.CountDownTimer;

public abstract class SimpleCountDownTimer extends CountDownTimer {
    public SimpleCountDownTimer(long millisInFuture, long countDownInterval) {
        super(millisInFuture, countDownInterval);
    }

    @Override
    public void onTick(long millisUntilFinished) {

    }

    @Override
    public abstract void onFinish();
}
