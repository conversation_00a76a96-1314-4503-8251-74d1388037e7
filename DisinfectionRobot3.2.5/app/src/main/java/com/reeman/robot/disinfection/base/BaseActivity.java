package com.reeman.robot.disinfection.base;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.os.Parcelable;
import android.text.TextUtils;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import androidx.annotation.IdRes;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.elvishew.xlog.XLog;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.reeman.robot.disinfection.BuildConfig;
import com.reeman.robot.disinfection.R;
import com.reeman.robot.disinfection.activities.SettingActivity;
import com.reeman.robot.disinfection.activities.TaskExecutingActivity;
import com.reeman.robot.disinfection.constants.Constant;
import com.reeman.robot.disinfection.constants.Errors;
import com.reeman.robot.disinfection.event.Event;
import com.reeman.robot.disinfection.request.RetrofitClient;
import com.reeman.robot.disinfection.utils.BatteryUtils;
import com.reeman.robot.disinfection.utils.ScreenUtils;
import com.reeman.robot.disinfection.utils.ToastUtils;
import com.reeman.robot.disinfection.utils.VoiceHelper;
import com.reeman.robot.disinfection.widgets.EasyDialog;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import io.reactivex.rxjava3.annotations.NonNull;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

import static com.reeman.robot.disinfection.base.BaseApplication.controller;

public abstract class BaseActivity extends AppCompatActivity {

    private int dockFailedCount = 0;

    private static final int REQUEST_CODE_FOR_GUIDE = 1000;

    private static final int REQUEST_CODE_FOR_ELEVATOR = 1001;

    protected Handler mHandler = new Handler(Looper.getMainLooper());

    public static final List<Activity> activities = new ArrayList<>();


    protected final Runnable chargeRunnable = new Runnable() {
        @Override
        public void run() {
            if (controller.getScramState() == 0) return;
            controller.chargeByPoint(getString(R.string.text_charging_pile));
        }
    };

    //findViewById
    public <T extends View> T $(@IdRes int id) {
        return findViewById(id);
    }

    //是否隐藏底部导航栏，由子类决定
    protected boolean disableBottomNavigationBar() {
        return true;
    }

    //获取布局
    protected abstract int getLayoutRes();

    //是否应该响应电量低和定时任务事件
    protected boolean shouldResponse2TimeEvent() {
        return false;
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        activities.add(this);
        Log.w(BuildConfig.TAG, this + "onCreate");
        if (disableBottomNavigationBar()) {
            ScreenUtils.hideBottomUIMenu(this);
        } else {
            ScreenUtils.setImmersive(this);
        }
        setContentView(getLayoutRes());
        initView();
        initData();
    }

    protected void initData() {

    }

    protected void initView() {
    }

    @Override
    protected void onStart() {
        super.onStart();
        Log.w(BuildConfig.TAG, this + "onStart");
    }

    @Override
    protected void onResume() {
        super.onResume();
        EventBus.getDefault().register(this);
        Log.w(BuildConfig.TAG, this + "onResume");
    }

    @Override
    protected void onPause() {
        super.onPause();
        EventBus.getDefault().unregister(this);
        Log.w(BuildConfig.TAG, this + "onPause");
    }

    @Override
    protected void onStop() {
        super.onStop();
        Log.w(BuildConfig.TAG, this + "onStop");
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        activities.remove(this);
        Log.w(BuildConfig.TAG, this + "onDestroy");
    }

    //充电对接失败
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onDockFailed(Event.OnDockFailedEvent event) {
        XLog.w("充电对接失败");
        onPrivateDockFailed();
    }

    //未找到充电桩
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onChargingPileNotFoundEvent(Event.OnChargeChargingPileNotFoundEvent event) {
        controller.setNavigating(false);
        XLog.w("未找到充电桩");
        if (EasyDialog.isShow()) EasyDialog.getInstance().dismiss();
        onCustomChargingPileNotFound();
    }

    //电量变化，电源连接断开
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onPowerEvent(Event.OnPowerEvent event) {
        Intent powerIntent = event.powerIntent;
        switch (powerIntent.getAction()) {
            case Intent.ACTION_BATTERY_CHANGED:
                onCustomBatteryChange(powerIntent);
                break;
            case Intent.ACTION_POWER_CONNECTED:
                uploadLogs();
                controller.setChargingDocking(false);
                onCustomPowerConnected();
                break;
            case Intent.ACTION_POWER_DISCONNECTED:
                onCustomPowerDisconnected();
                break;
        }
    }

    protected void uploadLogs() {
        File root = new File(Environment.getExternalStorageDirectory() + "/disinfection_robot");
        if (!root.exists()) return;
        File[] files = root.listFiles();
        if (files == null || files.length == 0) return;
        JsonArray jsonArray = new JsonArray();
        MultipartBody.Builder body = new MultipartBody.Builder().setType(MultipartBody.FORM);
        JsonObject singleParam;
        for (File file : files) {
            if (file.exists()) {
                singleParam = new JsonObject();
                singleParam.addProperty("project", "新版消毒");
                singleParam.addProperty("device", Event.getOnHostnameEvent().hostname);
                singleParam.addProperty("log", file.getName());
                singleParam.addProperty("file", file.getName());
                jsonArray.add(singleParam);
                body.addFormDataPart("uploadfiles", file.getName(), RequestBody.create(MediaType.parse("file/file"), file));
            }
        }
        JsonObject params = new JsonObject();
        params.addProperty("files", jsonArray.toString());
        RequestBody requestBody = body.addFormDataPart("information", params.toString()).build();
        Request request = new Request.Builder()
                .url("http://navi.rmbot.cn/logfile/uploadfiles")
                .post(requestBody)
                .build();
        OkHttpClient okHttpClient = RetrofitClient.getOkHttpClient();
        okHttpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(@NonNull Call call, @NonNull IOException e) {
                Log.w("日志上报：", e.toString());
//                if (BaseActivity.this instanceof SettingActivity)
//                    ToastUtils.showShortToast("fail:"+e.getMessage());
            }

            @Override
            public void onResponse(@NonNull Call call, @NonNull Response response) throws IOException {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
                String todayFileName = sdf.format(new Date());
                for (File file : files) {
                    if (todayFileName.equals(file.getName())) continue;
                    file.delete();
                }
//                if (BaseActivity.this instanceof SettingActivity)
//                    ToastUtils.showShortToast("success");
            }
        });

    }

    //急停开关状态改变
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEmergencyStopStateObtained(Event.OnEmergencyStopEvent event) {
        XLog.w("急停开关状态：" + event.emergencyStopState);
        if (event.emergencyStopState == 0) {
            controller.setChargingDocking(false);
            controller.setNavigating(false);
            mHandler.removeCallbacks(chargeRunnable);
        }
        onCustomEmergencyStopStateChange(event.emergencyStopState);
    }

    //导航结果
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onNavResEvent(Event.OnNavResEvent event) {
        controller.setNavigating(false);
        onCustomNavResEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onPointNotFoundEvent(Event.OnPointNotFoundEvent event) {
        controller.setNavigating(false);

        onCustomPointNotFoundEvent(event);
    }

    //子类自定义目标点找不到处理逻辑
    protected void onCustomPointNotFoundEvent(Event.OnPointNotFoundEvent event) {

    }


    //子类自定义导航结果处理逻辑，否则由父类同一处理
    protected void onCustomNavResEvent(Event.OnNavResEvent event) {
        try {
            String result = event.rawData.replace("nav_res:", "");
            JSONObject resultJson = new JSONObject(result);
            int res = resultJson.optInt("res");
            String dest = resultJson.optString("dest");
            String sensor = resultJson.optString("sensor");
            int reason = resultJson.optInt("reason");
            if (!getString(R.string.text_charging_pile).equals(dest)) {
                return;
            }
            if (res == 0) {
                controller.setChargingDocking(true);
                VoiceHelper.play("voice_start_docking_charging_pile");
                return;
            }
            if (EasyDialog.isShow()) EasyDialog.getInstance().dismiss();
            if (reason == 1) {
                EasyDialog.getInstance(this).warnError(Errors.getHardwareError(this, sensor));
            } else {
                EasyDialog.getInstance(this).warnError(Errors.getErrorByCode(this, reason));
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void onPrivateDockFailed() {
        if (++dockFailedCount < 2) {
            VoiceHelper.play("voice_charging_docking_failed_and_re_dock");
            mHandler.postDelayed(chargeRunnable, 4000);
            return;
        }
        dockFailedCount = 0;
        controller.setNavigating(false);
        controller.setChargingDocking(false);
        onCustomDockFailed();
    }

    //子类自定义充电对接失败处理逻辑
    protected void onCustomDockFailed() {
        if (EasyDialog.isShow()) EasyDialog.getInstance().dismiss();
        VoiceHelper.play("voice_charging_dock_failed");
        EasyDialog.getInstance(this).warnError(getString(R.string.voice_charging_dock_failed));
    }

    //子类自定义急停开关处理逻辑，默认关闭提示窗口
    protected void onCustomEmergencyStopStateChange(int emergencyStopState) {
        if (EasyDialog.isShow()) EasyDialog.getInstance().dismiss();
        controller.cancelNavigation();
        controller.cancelCharge();
    }

    //子类自定义充电处理逻辑，默认关闭充电对接提醒
    protected void onCustomPowerConnected() {
        VoiceHelper.play("voice_start_charging");
        if (EasyDialog.isShow()) EasyDialog.getInstance().dismiss();
    }

    //子类自定义断电处理逻辑
    protected void onCustomPowerDisconnected() {
        VoiceHelper.play("voice_stop_charge");
    }

    //子类自定义电量变化处理逻辑
    protected void onCustomBatteryChange(Intent powerIntent) {

    }

    //子类自定义找不到充电桩处理逻辑
    protected void onCustomChargingPileNotFound() {
        VoiceHelper.play("voice_not_found_charging_pile");
        EasyDialog.getInstance(this).warnError(getString(R.string.voice_not_found_charging_pile));
    }

    //子类自定义低电、定时任务处理逻辑
    protected void onCustomTimeStamp(Event.OnTimeEvent event) {
        //开始执行任务之前倒计时
        EasyDialog.getInstance(this).warnWithScheduledUpdateDetail(
                getString(Constant.RESPONSE_TIME, event),
                R.string.text_start_right_now,
                R.string.text_cancel_task,
                new EasyDialog.OnViewClickListener() {
                    @Override
                    public void onViewClick(Dialog dialog, int id) {
                        dialog.dismiss();
                        if (id == R.id.btn_confirm) startTask(event);
                    }
                },
                new EasyDialog.OnTimeStampListener() {
                    @Override
                    public void onTimestamp(TextView title, TextView content, Button cancelBtn, Button neutralBtn, Button confirmBtn, int current) {
                        content.setText(BaseActivity.this.getString(Constant.RESPONSE_TIME - current, event));
                    }

                    @Override
                    public void onTimeOut(EasyDialog dialog) {
                        dialog.dismiss();
                        startTask(event);
                    }
                },
                1000,
                Constant.RESPONSE_TIME * 1000
        );
    }


    //电量事件，定时任务
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onTimeStamp(Event.OnTimeEvent event) {
        //子类不想处理这些事件，返回
        XLog.w("检测是否符合定时任务状态： isNavigating: " + controller.isNavigating() + " isChargingDocking: " + controller.isChargingDocking());
        if (!shouldResponse2TimeEvent()) return;

        //现在在任务执行界面只处理低电
        if (activities.get(activities.size() - 1) instanceof TaskExecutingActivity) {
            onCustomTimeStamp(event);
            return;
        }

        //其它界面如果在导航中或者在充电对接中不处理
        if (controller.isNavigating() || controller.isChargingDocking()) return;
        onCustomTimeStamp(event);
    }

    //开始任务（充电或者开始定时任务）
    private void startTask(Event.OnTimeEvent event) {
        if (event.eventType == 0) {
            XLog.w("低电触发充电");
            controller.chargeByPoint(getString(R.string.text_charging_pile));
            EasyDialog
                    .getInstance(this)
                    .onlyCancel(getString(R.string.text_going_to_charge), new EasyDialog.OnViewClickListener() {
                        @Override
                        public void onViewClick(Dialog dialog, int id) {
                            dialog.dismiss();
                            if (controller.isNavigating()) {
                                controller.cancelNavigation();
                            }
                            if (controller.isChargingDocking()) {
                                controller.cancelCharge();
                                controller.setChargingDocking(false);
                            }
                        }
                    });
        } else {
            XLog.w("触发定时任务：" + event.task.toString());
            start(this, TaskExecutingActivity.class, event.task);
        }
    }

    private String getString(int secondsUntilFinished, Event.OnTimeEvent event) {
        String prompt;
        if (event.eventType == 0) {
            //充电事件
            prompt = getString(R.string.text_going_to_charge_for_low_power, secondsUntilFinished);
        } else {
            //定时任务
            prompt = getString(R.string.text_going_to_exec_schedule_task, secondsUntilFinished, event.task.taskName);
        }
        return prompt;
    }

    public static void start(Context context, Class<? extends Activity> clazz) {
        start(context, clazz, null);
    }

    public static void start(Context context, Class<? extends Activity> clazz, Parcelable extra) {
        Intent intent = new Intent(context, clazz);
        if (extra != null) {
            intent.putExtra(Constant.EXTRA, extra);
        }
        context.startActivity(intent);
    }

    public static void start(Activity context, Class<? extends Activity> clazz, String from, int extra) {
        Intent intent = new Intent(context, clazz);
        intent.putExtra(Constant.EXTRA, from);
        intent.putExtra(Constant.EXTRA_INT, extra);
        context.startActivityForResult(intent, REQUEST_CODE_FOR_GUIDE);
    }

    public static void startForResult(Activity context, Class<? extends Activity> clazz, String from) {
        Intent intent = new Intent(context, clazz);
        intent.putExtra(Constant.EXTRA, from);
        intent.putExtra(Constant.EXTRA_INT, 1001);
        context.startActivityForResult(intent, REQUEST_CODE_FOR_ELEVATOR);
    }

    //结束所有Activity
    public void finishAll() {
        for (Activity activity : activities) {
            activity.finish();
        }
    }

    //充电时按下物理按键前进一段距离
    @Override
    public boolean onKeyUp(int keyCode, KeyEvent event) {
        if (event.getKeyCode() == KeyEvent.KEYCODE_F2 && BatteryUtils.isWirelessCharging(this)) {
            controller.moveForward();
        }
        return super.onKeyUp(keyCode, event);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        XLog.e("resultCode :"+resultCode);
        if (resultCode == -1) {
            if (data == null) return;
            String voice = data.getStringExtra(Constant.TASK_RESULT_VOICE);
            if (!TextUtils.isEmpty(voice)) {
                VoiceHelper.play(voice);
            }
            String text = data.getStringExtra(Constant.TASK_RESULT_TEXT);
            XLog.e("text :"+text);
            if (!TextUtils.isEmpty(text)) {
                mHandler.postDelayed(() -> EasyDialog.getInstance(this).warnError(text), 1500);
            }
        }
    }
}
