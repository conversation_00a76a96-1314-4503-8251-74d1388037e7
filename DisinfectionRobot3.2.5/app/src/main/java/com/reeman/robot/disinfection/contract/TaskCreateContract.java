package com.reeman.robot.disinfection.contract;

import android.content.Context;

import com.reeman.robot.disinfection.presenter.IPresenter;
import com.reeman.robot.disinfection.view.IView;

public interface TaskCreateContract {

    interface Presenter extends IPresenter{


        void newTask(Context context, String taskName, int cycleMode, int backMode, int switchMode, String stayTime, String durationTime, String startTime, int repeatMode,String disinfectionFloors);
    }

    interface View extends IView {

        void onTaskSaved();

        void onTaskSaveFailed(String message);

        void showSavingTaskView();
    }
}
