package com.reeman.robot.disinfection.request.model;

public class State {
    private String name;
    private int emergency;
    private int battery;
    private int status;
    private String remarks;
    private String address;
    private String version;


    public State() {
    }

    public State(String name, int emergency, int battery, int status, String version) {
        this.name = name;
        this.emergency = emergency;
        this.battery = battery;
        this.status = status;
        this.version = version;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("State{");
        sb.append("name='").append(name).append('\'');
        sb.append(", emergency=").append(emergency);
        sb.append(", battery=").append(battery);
        sb.append(", status=").append(status);
        sb.append(", remarks='").append(remarks).append('\'');
        sb.append(", address='").append(address).append('\'');
        sb.append(", version='").append(version).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
