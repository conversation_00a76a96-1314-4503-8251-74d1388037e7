package com.reeman.robot.disinfection.event;

import android.content.Intent;
import android.text.TextUtils;

import com.google.gson.annotations.SerializedName;
import com.reeman.robot.disinfection.repository.entities.Task;
import com.reeman.robot.disinfection.request.model.Room;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: Event.java
 * @Author: XueDong(1123988589 @ qq.com)
 * @Date: 2022/1/9 19:42
 * @Description: 事件对象
 */
public class Event {

    private static final OnVersionEvent onVersionEvent = new OnVersionEvent();
    private static final OnHostnameEvent onHostnameEvent = new OnHostnameEvent();
    private static final OnIpEvent onIpEvent = new OnIpEvent();
    private static final OnNavResEvent onNavResEvent = new OnNavResEvent();
    private static final OnPointNotFoundEvent onPointNotFoundEvent = new OnPointNotFoundEvent();
    private static final OnChargeChargingPileNotFoundEvent onChargePointNotFoundEvent = new OnChargeChargingPileNotFoundEvent();
    private static final OnMarkSuccessEvent onMarkSuccessEvent = new OnMarkSuccessEvent();
    private static final OnMarkFailedEvent onMarkFailedEvent = new OnMarkFailedEvent();
    private static final OnTimeEvent onTimeEvent = new OnTimeEvent();
    private static final OnPowerEvent onPowerEvent = new OnPowerEvent();
    private static final OnChargeFailEvent onChargeFailEvent = new OnChargeFailEvent();
    private static final OnEmergencyStopEvent onEmergencyStopEvent = new OnEmergencyStopEvent();
    private static final OnNetworkEvent onNetworkEvent = new OnNetworkEvent();
    private static final OnEncounterObstacleEvent onEncounterObstacleEvent = new OnEncounterObstacleEvent();
    private static final OnDockFailedEvent onDockFailedEvent = new OnDockFailedEvent();
    private static final OnMapEvent onMapEvent = new OnMapEvent();
    private static final OnNavModeEvent onNavModeEvent = new OnNavModeEvent();
    private static final OnInitPoseEvent onInitPoseEvent = new OnInitPoseEvent();
    private static final OnPositionEvent onPositionEvent = new OnPositionEvent();
    private static final OnPointFoundEvent onPointFoundEvent = new OnPointFoundEvent();
    private static final OnSpeedEvent onSpeedEvent = new OnSpeedEvent();
    private static final OnWiFiEvent onWiFiEvent = new OnWiFiEvent();
    private static final OnApplyMapEvent onApplyMapEvent = new OnApplyMapEvent();
    private static final OnTaskUpdateEvent onTaskUpdateEvent = new OnTaskUpdateEvent();
    private static final OnMostRecentTaskLoadEvent onMostRecentTaskLoadEvent = new OnMostRecentTaskLoadEvent();
    private static final OnSpecialPlanEvent onSpecialPlanEvent = new OnSpecialPlanEvent();

    public static OnSpecialPlanEvent getOnSpecialPlanEvent(String str) {
        List<Room> list = new ArrayList<>();
        try {
            JSONObject jsonObject = new JSONObject(str);
            JSONArray sp = jsonObject.optJSONArray("sp");
            if (sp != null) {
                for (int i = 0; i < sp.length(); i++) {
                    JSONObject temp = sp.getJSONObject(i);
                    Room room = new Room();
                    room.name = temp.optString("n");
                    if (TextUtils.isEmpty(room.name)) continue;
                    JSONArray c = temp.optJSONArray("c");
                    if (c == null || c.length() == 0) continue;
                    room.coordination = new ArrayList<>();
                    for (int i1 = 0; i1 < c.length(); i1++) {
                        room.coordination.add(c.optDouble(i1));
                    }
                    list.add(room);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        onSpecialPlanEvent.rooms = list;
        return onSpecialPlanEvent;
    }

    public static OnSpecialPlanEvent getOnSpecialPlanEvent() {
        return onSpecialPlanEvent;
    }

    public static OnMostRecentTaskLoadEvent getOnMostRecentTaskLoadEvent(Task task) {
        onMostRecentTaskLoadEvent.task = task;
        return onMostRecentTaskLoadEvent;
    }

    public static OnTaskUpdateEvent getOnTaskUpdateEvent(List<Task> list) {
        onTaskUpdateEvent.list = list;
        return onTaskUpdateEvent;
    }

    public static OnApplyMapEvent getOnApplyMapEvent(String result) {
        onApplyMapEvent.map = result;
        return onApplyMapEvent;
    }

    public static OnWiFiEvent getOnWiFiEvent(boolean result) {
        onWiFiEvent.isConnect = result;
        return onWiFiEvent;
    }

    public static OnSpeedEvent getOnSpeedEvent(String result) {
        onSpeedEvent.speed = Float.parseFloat(result);
        return onSpeedEvent;
    }

    public static OnVersionEvent getVersionEvent(String result) {
        onVersionEvent.rawData = result;
        onVersionEvent.version = result.replace("ver:", "");
        StringBuilder vc = new StringBuilder();
        for (int length = onVersionEvent.version.length()-1; length >= 0; length--) {
            try {
                int i = Integer.parseInt(String.valueOf(onVersionEvent.version.charAt(length)));
                vc.insert(0, i);
                if (vc.length() == 3) {
                    onVersionEvent.versionCode = Integer.parseInt(vc.toString());
                    break;
                }
            } catch (Exception e) {

            }
        }

        return onVersionEvent;
    }

    public static int getVersionCodeEvent(){
        return onVersionEvent.versionCode;
    }

    public static OnHostnameEvent getHostnameEvent(String result) {
        onHostnameEvent.rawData = result;
        onHostnameEvent.hostname = result.replace("sys:boot:", "");
        return onHostnameEvent;
    }

    public static OnHostnameEvent getOnHostnameEvent() {
        return onHostnameEvent;
    }

    public static OnIpEvent getIpEvent(String result) {
        onIpEvent.rawData = result;
        String[] split = result.split(":");
        if (split.length == 3) {
            onIpEvent.wifiName = split[1];
            onIpEvent.ipAddress = split[2];
        } else {
            onIpEvent.wifiName = "未连接";
            onIpEvent.ipAddress = "127.0.0.1";
        }
        return onIpEvent;
    }

    public static OnIpEvent getIpEvent() {
        return onIpEvent;
    }

    public static OnNavResEvent getNavResEvent(String result) {
        onNavResEvent.rawData = result;
        return onNavResEvent;
    }

    public static OnMapEvent getMapEvent() {
        return onMapEvent;
    }

    public static OnMapEvent getMapEvent(String result) {
        onMapEvent.map = result.replace("current_map[map_name:", "").replace("]", "");
        return onMapEvent;
    }

    public static OnPointNotFoundEvent getPointNotFoundEvent() {
        return onPointNotFoundEvent;
    }

    public static OnChargeChargingPileNotFoundEvent getChargePointNotFoundEvent() {
        return onChargePointNotFoundEvent;
    }

    public static OnMarkSuccessEvent getMarkSuccessEvent() {
        return onMarkSuccessEvent;
    }

    public static OnMarkFailedEvent getMarkFailedEvent() {
        return onMarkFailedEvent;
    }

    public static OnTimeEvent getTimeEvent(int type, Task task) {
        onTimeEvent.eventType = type;
        onTimeEvent.task = task;
        return onTimeEvent;
    }

    public static OnPowerEvent getPowerEvent(Intent intent) {
        onPowerEvent.powerIntent = intent;
        return onPowerEvent;
    }

    public static OnChargeFailEvent getChargeFailEvent() {
        return onChargeFailEvent;
    }

    public static OnEmergencyStopEvent getEmergencyStopEvent(Intent intent) {
        onEmergencyStopEvent.emergencyStopState = intent.getIntExtra("SCRAM_STATE", -1);
        return onEmergencyStopEvent;
    }

    public static OnEmergencyStopEvent getOnEmergencyStopEvent() {
        return onEmergencyStopEvent;
    }

    public static OnNetworkEvent getNetworkEvent(Intent intent) {
        onNetworkEvent.networkIntent = intent;
        return onNetworkEvent;
    }

    public static OnEncounterObstacleEvent getEncounterObstacleEvent() {
        return onEncounterObstacleEvent;
    }

    public static OnDockFailedEvent getDockFailedEvent() {
        return onDockFailedEvent;
    }

    public static OnNavModeEvent getOnNavModeEvent(int mode) {
        onNavModeEvent.mode = mode;
        return onNavModeEvent;
    }

    public static OnInitPoseEvent getOnInitPoseEvent() {
        return onInitPoseEvent;
    }

    public static OnPositionEvent getOnPositionEvent(String res) {
        onPositionEvent.position = res.split(",");
        return onPositionEvent;
    }

    public static OnNavResEvent getOnNavResEvent() {
        return onNavResEvent;
    }

    public static OnPointFoundEvent getOnPointFoundEvent() {
        return onPointFoundEvent;
    }

    public static class BaseEvent {
        public String rawData;
    }

    public static class OnVersionEvent extends BaseEvent {
        public String version;
        public int versionCode;
    }

    public static class OnHostnameEvent extends BaseEvent {
        public String hostname;
    }

    public static class OnIpEvent extends BaseEvent {
        public String wifiName;
        public String ipAddress;
    }

    public static class OnNavResEvent extends BaseEvent {

    }

    public static class OnMapEvent extends BaseEvent {
        public String map;
    }

    public static class OnPositionEvent extends BaseEvent {
        public String[] position;
    }

    public static class OnNavModeEvent {

        public int mode;
    }

    public static class OnPointNotFoundEvent {

    }

    public static class OnChargeChargingPileNotFoundEvent {

    }

    public static class OnMarkSuccessEvent {

    }

    public static class OnMarkFailedEvent {

    }

    public static class OnTimeEvent {
        public int eventType;
        public Task task;
    }

    public static class OnPowerEvent {

        public Intent powerIntent;
    }

    public static class OnChargeFailEvent {

    }

    public static class OnEncounterObstacleEvent {

    }

    public static class OnDockFailedEvent {

    }

    public static class OnInitPoseEvent {

    }

    public static class OnPointFoundEvent {

    }

    public static class OnEmergencyStopEvent {
        public int emergencyStopState;
    }

    public static class OnNetworkEvent {
        public Intent networkIntent;
    }

    public static class OnSpeedEvent {
        public float speed;
    }

    public static class OnWiFiEvent {
        public boolean isConnect;
    }

    public static class OnApplyMapEvent {
        public String map;
    }

    public static class OnTaskUpdateEvent {
        public List<Task> list;
    }

    public static class OnMostRecentTaskLoadEvent {
        public Task task;
    }

    public static class OnSpecialPlanEvent {

        @SerializedName("sp")
        public List<Room> rooms;

    }
}
