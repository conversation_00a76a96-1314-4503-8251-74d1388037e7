package com.reeman.robot.disinfection.widgets;

import android.content.Context;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.ListPopupWindow;
import android.widget.TextView;

import com.reeman.robot.disinfection.R;

import java.util.List;

public class ChooseItemPopupWindow extends ListPopupWindow {

    public ChooseItemPopupWindow(Context context, View anchor, String title, List<String> items) {
        super(context);
        setAnchorView(anchor);
        setWidth(500);
        setHeight(360);
        int centerX = (int) (anchor.getX() + anchor.getWidth() / 2);
        setHorizontalOffset((int) (centerX - (anchor.getX() + 250)));
        setVerticalOffset((int) (anchor.getY() + 40));
        View promptView = LayoutInflater.from(context).inflate(R.layout.layout_pop_up_window_prompt, null);
        TextView tvTitle = promptView.findViewById(R.id.tv_pop_up_window_title);
        tvTitle.setText(title);
        setBackgroundDrawable(context.getResources().getDrawable(R.drawable.bg_common_dialog));
        setAnimationStyle(R.style.popupWindowAlphaAnimation);
        setPromptView(promptView);
        setAdapter(new ArrayAdapter<>(context, R.layout.layout_popup_item, R.id.tv_spinner_item, items));
        setDropDownGravity(Gravity.CENTER);
        setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                if (onItemChosenListener != null) {
                    onItemChosenListener.onSpeedChosen(ChooseItemPopupWindow.this, items.get(position));
                }
            }
        });
    }

    private OnItemChosenListener onItemChosenListener;

    public void setOnItemChosenListener(OnItemChosenListener onItemChosenListener) {
        this.onItemChosenListener = onItemChosenListener;
    }

    public interface OnItemChosenListener {
        void onSpeedChosen(ListPopupWindow window, String speed);
    }
}
