<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.reeman.robot.disinfection">

    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission
        android:name="android.permission.CHANGE_CONFIGURATION"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.READ_LOGS"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS" />

    <application
        android:name=".base.BaseApplication"
        android:allowBackup="true"
        android:icon="@mipmap/app_icon"
        android:label="@string/text_app_name"
        android:roundIcon="@mipmap/app_icon"
        android:supportsRtl="true"
        android:theme="@style/Theme.DisinfectionRobot">
        <activity android:name=".activities.LoginActivity"/>
        <activity android:name=".activities.RobotTypeChooseActivity" />
        <activity android:name=".activities.LanguageChooseActivity" />
        <activity android:name=".activities.GuideActivity" />
        <activity
            android:name=".activities.TaskExecutingActivity"
            android:exported="false" />
        <activity android:name=".activities.WiFiConnectActivity" />
        <activity android:name=".activities.MapBuildingActivity" />
        <activity android:name=".activities.SettingActivity" />
        <activity android:name=".activities.TaskCreateActivity" />
        <activity android:name=".activities.ScheduledTaskListActivity" />
        <activity android:name=".activities.MainActivity" />
        <activity android:name=".activities.ElevatorSettingActivity"/>
        <activity android:name=".activities.PortMappingActivity"/>
        <activity android:name=".SplashActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <service android:name=".service.BackendService" />
    </application>

</manifest>