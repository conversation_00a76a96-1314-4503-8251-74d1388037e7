<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.DisinfectionRobot" parent="Theme.MaterialComponents.DayNight.NoActionBar.Bridge">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor" tools:targetApi="l">?attr/colorPrimaryVariant</item>
        <!-- Customize your theme here. -->

        <item name="android:dialogTheme">@style/Dialog</item>
    </style>

    <style name="EditTextStyle" parent="Theme.AppCompat.Light">
        <item name="colorControlNormal">@android:color/white</item>
        <item name="colorControlActivated">@android:color/white</item>
    </style>

    <style name="common_dialog_style" parent="android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@drawable/bg_common_dialog</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@style/windowAnimation</item>
    </style>

    <style name="elevator_dialog_style" parent="android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@drawable/bg_common_dialog</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@style/windowAnimation</item>
    </style>

    <style name="windowAnimation" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/scale_in</item>
        <item name="android:windowExitAnimation">@anim/scale_out</item>
    </style>


    <style name="Dialog" parent="android:style/Theme.Holo.DialogWhenLarge">
        <item name="android:background">@android:color/white</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
    </style>

    <style name="popupWindowTranslateAnimation">
        <item name="android:windowEnterAnimation">@anim/pop_window_in</item>
        <item name="android:windowExitAnimation">@anim/pop_window_out</item>
    </style>

    <style name="SwitchButtonStyle">
        <item name="android:paddingLeft">10dp</item>
        <item name="android:paddingRight">10dp</item>
        <item name="android:paddingTop">4dp</item>
        <item name="android:paddingBottom">4dp</item>
    </style>

    <style name="popupWindowAlphaAnimation">
        <item name="android:windowEnterAnimation">@anim/alpha_in</item>
        <item name="android:windowExitAnimation">@anim/alpha_out</item>
    </style>

</resources>