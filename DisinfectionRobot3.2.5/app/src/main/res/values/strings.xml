<resources>
    <!--适配在线升级-->
    <string name="strNetworkTipsCancelBtn">取消</string>
    <string name="strNetworkTipsConfirmBtn">继续下载</string>
    <string name="strNetworkTipsMessage">你已切换到移动网络，是否继续当前下载？</string>
    <string name="strNetworkTipsTitle">网络提示</string>
    <string name="strNotificationClickToContinue">继续下载</string>
    <string name="strNotificationClickToInstall">点击安装</string>
    <string name="strNotificationClickToRetry">点击重试</string>
    <string name="strNotificationClickToView">点击查看</string>
    <string name="strNotificationDownloadError">下载失败</string>
    <string name="strNotificationDownloadSucc">下载完成</string>
    <string name="strNotificationDownloading">正在下载</string>
    <string name="strNotificationHaveNewVersion">有新版本</string>
    <string name="strToastCheckUpgradeError">检查新版本失败，请稍后重试</string>
    <string name="strToastCheckingUpgrade">正在检查，请稍候...</string>
    <string name="strToastYourAreTheLatestVersion">你已经是最新版了</string>
    <string name="strUpgradeDialogCancelBtn">下次再说</string>
    <string name="strUpgradeDialogContinueBtn">继续</string>
    <string name="strUpgradeDialogFeatureLabel">更新说明</string>
    <string name="strUpgradeDialogFileSizeLabel">包大小</string>
    <string name="strUpgradeDialogInstallBtn">安装</string>
    <string name="strUpgradeDialogRetryBtn">重试</string>
    <string name="strUpgradeDialogUpdateTimeLabel">更新时间</string>
    <string name="strUpgradeDialogUpgradeBtn">立即更新</string>
    <string name="strUpgradeDialogVersionLabel">版本</string>


    <!--适配PickerView-->
    <string name="pickerview_cancel">取消</string>
    <string name="pickerview_submit">确定</string>

    <string name="text_app_name">消毒机器人-梯控</string>
    <string name="text_charging_pile">充电桩</string>
    <string name="text_choose_map">选择地图</string>
    <string name="text_title_recent_task">今日最近任务</string>
    <string name="text_title_robot_state">机器状态</string>
    <string name="text_look_up_recent_task">查看最近任务</string>
    <string name="text_go_to_charge">充电</string>
    <string name="text_title_manual_task">手动消毒模式</string>
    <string name="text_start_disinfection">开始消毒</string>
    <string name="text_lock_screen">锁屏</string>
    <string name="text_choose_wifi">WiFi设置</string>
    <string name="text_build_map">地图部署</string>
    <string name="text_current_map">当前地图</string>
    <string name="text_current_wifi">导航WIFI</string>
    <string name="text_current_ip">导航主机IP</string>
    <string name="text_title_task_setting">任务设置</string>
    <string name="text_back">返回</string>
    <string name="text_single_mode">单次执行</string>
    <string name="text_duration_mode">时长循环</string>
    <string name="text_destination_when_task_complete">消毒完成</string>
    <string name="text_go_to_starting_point_when_task_finished">出发点</string>
    <string name="text_disinfection_switch">消毒开关</string>
    <string name="text_open_all_the_way">全程消毒</string>
    <string name="text_open_in_target_point">目标点消毒</string>
    <string name="text_close_all_the_way">全程关闭</string>
    <string name="text_stay_time">点位停留</string>
    <string name="text_duration_time">循环时长</string>
    <string name="text_cycle_mode">消毒模式</string>
    <string name="text_invalid_time_in_duration_mode">循环时长不合法(大于5秒)</string>
    <string name="text_open_in_target_point_when_stay_time_is_0">停留时长为0时请勿设置在目标点消毒</string>
    <string name="text_title_system_setting">系统设置</string>
    <string name="text_account">当前账号</string>
    <string name="text_not_login">未登录</string>
    <string name="text_choice_voice_broadcast">语音播报</string>
    <string name="text_open">打开</string>
    <string name="text_close">关闭</string>
    <string name="text_choice_lock_screen">锁屏设置</string>
    <string name="text_choice_speed">运行速度</string>
    <string name="text_low_power">自动充电</string>
    <string name="text_choice_volume">音量调节</string>
    <string name="text_delay_disinfection">任务倒计时</string>
    <string name="text_second">秒</string>
    <string name="text_current_version">当前版本</string>
    <string name="text_invalid_low_power">自动充电电量不合法(10~80)</string>
    <string name="text_choose_speed">选择运行速度(m/s)</string>
    <string name="text_title_scheduled_task_list">计划任务列表</string>
    <string name="text_task_name">任务名称</string>
    <string name="text_repeat_date">重复日期</string>
    <string name="text_repeat_monday">周一</string>
    <string name="text_repeat_sunday">周日</string>
    <string name="text_repeat_saturday">周六</string>
    <string name="text_repeat_friday">周五</string>
    <string name="text_repeat_thursday">周四</string>
    <string name="text_repeat_wednesday">周三</string>
    <string name="text_repeat_tuesday">周二</string>
    <string name="text_start_time">开始时间</string>
    <string name="text_invalid_task_name">任务名称不能为空</string>
    <string name="text_default_scheduled_task_name">定时任务%1$s</string>
    <string name="text_create_disinfection_task">创建消毒任务</string>
    <string name="text_modify_disinfection_task">修改消毒任务</string>
    <string name="text_save">保存</string>
    <string name="text_create">创建</string>
    <string name="text_task_enabled">定时任务已启用</string>
    <string name="text_task_disabled">定时任务已禁用</string>
    <string name="text_date_format">HH:mm:ss</string>
    <string name="text_invalid_repeat_time">请选择工作日</string>
    <string name="text_enter_lock_screen">请输入锁屏密码</string>
    <string name="text_cancel">取消</string>
    <string name="text_confirm">确定</string>
    <string name="text_enter_lock_screen_password">请输入锁屏密码"</string>
    <string name="text_password_error">密码错误，请重新输入</string>
    <string name="text_enter_lock_screen_password_again">请再次输入锁屏密码</string>
    <string name="text_invalid_lock_screen_password">不合法的锁屏密码(4位数字)</string>
    <string name="text_passwords_are_not_same">两次密码不一致，请重新输入</string>
    <string name="text_rebuild_map">重新建图</string>
    <string name="text_set_disinfection_point">添加消毒点</string>
    <string name="text_set_charging_pile">标注充电桩</string>
    <string name="text_exit_deploy">退出部署</string>
    <string name="text_save_map">保存地图</string>
    <string name="text_disinfection_point_test">路线测试</string>
    <string name="text_current_host_name">导航主机: %1$s</string>
    <string name="text_dialog_title_prompt">提示</string>
    <string name="text_emergency_stop_turn_on">急停开关按下</string>
    <string name="text_charging">机器人充电中</string>
    <string name="text_low_pow">机器人电量低</string>
    <string name="text_title_task_state">消毒状态</string>
    <string name="text_current_task_type">任务类型: %1$s</string>
    <string name="text_current_task_mode">消毒模式: %1$s</string>
    <string name="text_current_task_start_time">开始时间: %1$s</string>
    <string name="text_current_task_time_waste">消毒时长: %1$s</string>
    <string name="text_current_disinfection_switch_state">消毒开关: %1$s</string>
    <string name="text_current_destination">正在前往: %1$s</string>
    <string name="text_current_task_time_remain">剩余时间: %1$s</string>
    <string name="text_finish_task">结束任务</string>
    <string name="text_pause_task">暂停任务</string>
    <string name="text_confirm_add_disinfection_point_at_this_location">确认将当前位置设置为%1$d号消毒点吗?</string>
    <string name="text_scheduled_task">定时任务</string>
    <string name="text_resume_task">恢复任务</string>
    <string name="text_manual_task">手动任务</string>
    <string name="text_time_duration_format2">%02d:%02d:%02d</string>
    <string name="text_time_duration_format">%02d:%02d:%02d</string>
    <string name="text_can_not_find_starting_point">未找到消毒起始点，请检查是否标注消毒点位</string>
    <string name="text_closed">已关闭</string>
    <string name="text_opened">已打开</string>
    <string name="text_wifi_enabling">正在打开</string>
    <string name="text_wifi_disabling">正在关闭</string>
    <string name="text_current_android_wifi">安卓连接: %1$s</string>
    <string name="text_open_wifi_first">请先打开WIFI</string>
    <string name="text_current_ros_wifi">导航连接: %1$s</string>
    <string name="text_already_updated">已更新</string>
    <string name="text_wifi_auth">WiFi身份认证</string>
    <string name="text_wifi_name">WiFi名称</string>
    <string name="text_wifi_password">WiFi密码</string>
    <string name="text_auth">认证</string>
    <string name="text_wifi_password_can_not_be_empty">密码不能为空</string>
    <string name="text_connect_time_out">连接超时</string>
    <string name="text_connect_success">连接成功</string>
    <string name="text_connect_failed">连接失败</string>
    <string name="text_going_to_charge_for_low_power">电量过低，将在%1$d秒后前往充电桩充电</string>
    <string name="text_going_to_exec_schedule_task">将在%1$d秒后开始定时任务[%2$s]</string>
    <string name="text_confirm_delete_this_task">确认删除当前定时任务吗？</string>
    <string name="text_going_to_charge">正在前往充电桩充电</string>
    <string name="text_going_to_start_point">正在返回出发点</string>
    <string name="text_task_was_canceled">任务已被取消</string>
    <string name="text_going_to_start_task_in_future">将在%1$d秒后开始任务</string>
    <string name="text_navigating_to_target_point_for_test">正在前往%1$s号消毒点进行测试</string>
    <string name="text_going_to_charging_pile_for_charge_test">正在前往充电桩进行充电对接测试</string>
    <string name="text_test_failed_for_not_found_start_point">路线测试失败，未找到消毒起始点</string>
    <string name="text_test_result_charger_not_found">充电对接失败，未找到充电桩</string>
    <string name="text_test_result_success">消毒点位测试成功，充电对接测试成功</string>
    <string name="text_test_result_canceled_by_user">测试任务已被手动取消</string>
    <string name="text_exit_app">退出应用</string>
    <string name="text_enter_app">请进入应用</string>
    <string name="text_next_step">下一步</string>
    <string name="text_restart_for_configuration_change">应用配置发生改变，即将重新进入应用</string>
    <string name="text_robot_type_choose">机型选择</string>
    <string name="text_atomization_disinfection_robot">雾化消毒机器人</string>
    <string name="text_ultraviolet_disinfection_robot">紫外线消毒机器人</string>
    <string name="text_please_enter_username">请输入账号</string>
    <string name="text_skip">跳过</string>
    <string name="text_please_enter_password">请输入密码</string>
    <string name="text_username_can_not_be_empty">用户名不能为空</string>
    <string name="text_login_failed">登录失败</string>
    <string name="text_login_success">登录成功</string>
    <string name="text_login">登录</string>
    <string name="text_exit_login">退出登录</string>
    <string name="text_please_enter_lock_password">请输入锁屏密码</string>
    <string name="text_delete"><![CDATA[<]]></string>
    <string name="text_please_choose_map">请选择地图</string>
    <string name="text_loading_map_list">正在加载地图，请稍候...</string>
    <string name="text_changing_map">正在切换地图，请稍候...</string>
    <string name="text_change_map_success">地图切换成功</string>
    <string name="text_map_load_failed">地图加载失败</string>
    <string name="text_wifi_not_connect">未连接</string>
    <string name="text_finish">完成</string>
    <string name="text_language_setting">当前语言</string>
    <string name="text_saving_task">正在保存任务，请稍后...</string>
    <string name="text_task_saved_success">任务保存成功</string>
    <string name="text_task_already_update">任务已同步</string>
    <string name="text_is_login">正在登录，请稍候...</string>
    <string name="text_speed_unit">m/s</string>
    <string name="text_wifi_name_can_not_be_empty">WiFi名称不能为空</string>
    <string name="text_android_wifi_connect_failed">Android网络连接失败</string>
    <string name="text_ros_wifi_connect_failed">导航WiFi连接失败</string>
    <string name="text_route_test_success">消毒路线测试成功</string>
    <string name="text_route_test_failed">消毒路线测试失败，消毒点位%1$s未能正常到达</string>
    <string name="text_test_result_charging_docking_success">充电对接成功</string>
    <string name="text_test_result_charging_docking_failed">充电对接失败</string>
    <string name="text_navigation_version">导航版本</string>
    <string name="text_back_to_main_page">放弃构图</string>
    <string name="text_disinfection_voice">消毒中语音</string>
    <string name="text_synthesize_audio">试听</string>
    <string name="text_input_disinfection_prompt_first">请先输入机器人正在消毒中提示音频对应的文字</string>
    <string name="text_not_found_audio_file">未找到相关音频文件，请先合成提示音频</string>
    <string name="text_save_success">保存成功</string>
    <string name="text_page_load_failed">网页加载超时，请确保安卓和导航主机连接同一网络</string>
    <string name="text_ros_wifi">导航WiFi: %1$s</string>
    <string name="text_ros_ip">导航IP: %1$s</string>
    <string name="text_wechat_app">绑定账号</string>
    <string name="text_click_to_bind">点击绑定账号</string>
    <string name="text_open_wechat">第一步:\n请打开微信扫描下方二维码登录您的机器人账号</string>
    <string name="text_scan_code_to_bind_machine">第二步:\n请使用小程序扫描下方二维码绑定机器</string>
    <string name="text_save_task">保存任务</string>
    <string name="text_switch">切换</string>
    <string name="text_invalid_delay_time">请输入延时消毒时长</string>
    <string name="text_logout_success">已退出登录</string>
    <string name="text_start_right_now">立即开始</string>
    <string name="text_cancel_task">取消任务</string>
    <string name="text_do_not_apply_map_repeatedly">请勿重复切换地图</string>
    <string name="text_synthesize_error">音频生成失败：%1$s</string>


    <string name="exception_remote_cancel_navigation">当前任务已被取消</string>
    <string name="exception_encounter_obstacle_in_the_way">导航途中遭遇障碍物，任务失败</string>
    <string name="exception_encounter_obstacle_in_target_point">目标点存在障碍物，任务失败</string>
    <string name="exception_not_in_work_space">当前处于工作区外，任务失败</string>
    <string name="exception_forbidden_zone">进入禁区，任务失败</string>
    <string name="exception_tripped_in_obstacle">陷入障碍物中或过于靠近墙体，任务失败</string>
    <string name="exception_laser_error">激光异常，请联系技术人员</string>
    <string name="exception_speedometer_error">里程计异常，请联系技术人员</string>
    <string name="exception_chassis_failure">底盘故障，请联系技术人员</string>
    <string name="exception_imu_error">IMU故障，请联系技术人员</string>
    <string name="exception_chassis_over_current">底盘过流，请联系技术人员</string>
    <string name="exception_new_target">收到导航新目标，任务失败</string>
    <string name="exception_encounter_obstacle_in_target_point_or_in_the_way">导航途中或目标点存在障碍物，导航失败</string>
    <string name="exception_visual_mark">标签定位异常</string>

    <string name="voice_connecting_wifi">正在连接网络，请稍候...</string>
    <string name="voice_select_robot_type">请选择型号</string>
    <string name="voice_please_connect_wifi">请配置网络</string>
    <string name="voice_please_login">请登录账号</string>
    <string name="voice_please_enter_app">请进入应用</string>
    <string name="voice_click_to_rebuild_map">重新建图将放弃当前扫描的地图并从该位置开始重新扫描地图,请确认您的操作</string>
    <string name="voice_entering_map_building_mode">正在进入建图模式，请稍候...</string>
    <string name="voice_entering_nav_mode">正在进入导航模式，请稍候...</string>
    <string name="voice_current_in_nav_mode">当前为导航模式，您可以进行消毒点位的标注以及消毒路线的测试</string>
    <string name="voice_current_in_map_building_mode">当前为建图模式，请按下急停开关，然后从后方推动机器人开始扫描地图。</string>
    <string name="voice_click_to_abandon_map_building">返回主界面将不会保存当前扫描的地图，请确定您的操作</string>
    <string name="voice_click_to_save_map">即将保存地图并退出建图模式，请确认您的操作</string>
    <string name="voice_saving_map_and_entering_nav_mode">正在保存地图并进入导航模式，请稍候...</string>
    <string name="voice_confirm_set_charging_pile_at_this_location">请确保机器人正确对接在充电桩上，然后点击确定将当前位置设置为充电桩</string>
    <string name="voice_confirm_add_disinfection_point_at_this_location">确认将当前位置设置为消毒点吗？</string>
    <string name="voice_target_point_mark_success">目标点位标注成功</string>
    <string name="voice_target_point_mark_failed">目标点位标注失败</string>
    <string name="voice_confirm_exit_deploy">确认退出地图部署吗？请确保已完成消毒点以及充电桩的标注，并成功进行消毒路线测试</string>
    <string name="voice_going_to_start_disinfection_route_test">即将开始消毒路线测试，全程将不会打开消毒开关，请确保已完成消毒点位和充电桩位置的标注</string>
    <string name="voice_emergency_stop_turn_on">我的急停开关被按下了，不能移动了</string>
    <string name="voice_turn_off_scram_stop_to_start_task">请先关闭急停开关再开始任务</string>
    <string name="voice_cut_off_power_to_start_task">请先断开电源适配器再开始任务</string>
    <string name="voice_going_to_start_disinfection_task">我要开始执行消毒任务了</string>
    <string name="voice_stay_away_from_me">正在消毒中，请不要靠近我</string>
    <string name="voice_task_finish_and_go_to_charge">任务完成，我要去充电了</string>
    <string name="voice_task_finish_and_go_to_start_point">任务完成，我要返回出发点了</string>
    <string name="voice_start_docking_charging_pile">到达充电区域，开始对接充电桩</string>
    <string name="voice_start_charging">开始充电</string>
    <string name="voice_task_finished">任务完成</string>
    <string name="voice_already_in_charging">我正在充电中</string>
    <string name="voice_going_to_charging">好的，这就去充电</string>
    <string name="voice_not_guide_build_map">检测到您是第一次使用，需要我来帮助您部署地图吗？</string>
    <string name="voice_guide_goto_setting">点击此处进入设置界面</string>
    <string name="voice_guide_exit_app">连续点击此处两次后从屏幕下方向上划出导航栏可以退出应用</string>
    <string name="voice_guide_goto_charge">点击此处下发充电指令</string>
    <string name="voice_guide_look_up_recent_task">点击此处查看定时任务</string>
    <string name="voice_guide_start_disinfection">点击此处开始消毒任务</string>
    <string name="voice_guide_goto_manual_task">点击此处设置手动任务</string>
    <string name="voice_guide_finish">恭喜您完成了操作引导！</string>
    <string name="voice_guide_more_actions">从屏幕右侧往左滑动可以进行更多操作</string>
    <string name="voice_not_found_charging_pile">未找到充电桩</string>
    <string name="voice_charging_docking_failed_and_re_dock">充电对接失败，正在尝试重新对接</string>
    <string name="voice_charging_dock_failed">自动充电失败</string>
    <string name="voice_route_test_finished">消毒路线测试完成</string>
    <string name="voice_stop_charge">停止充电</string>
    <string name="voice_low_power_go_to_charge">电量过低，我要去充电了</string>
    <string name="voice_wifi_connect_success">网络连接成功</string>
    <string name="voice_login_success">登录成功</string>
    <string name="text_gating_serial_port">门控串口</string>
    <string name="text_elevator_serial_port">梯控串口</string>
    <string name="text_can_not_find_serial_port">找不到串口</string>
    <string name="text_gating_port_elevator_port_same">门控和梯控不能使用同一个串口</string>
    <string name="text_gating_switch">门控开关</string>
    <string name="text_elevator_switch">梯控开关</string>
    <string name="text_gating_port_open_failed">门控串口开启失败</string>
    <string name="text_open_door_success">开门成功</string>
    <string name="text_close_door_success">关门成功</string>
    <string name="text_elevator_ip_port">梯控IP和端口</string>
    <string name="text_write_right_ip">请输入正确的IP</string>
    <string name="text_floor_choose">楼层选择</string>
    <string name="text_elevator">电梯</string>
    <string name="text_floor_scope">楼层范围</string>
    <string name="text_not_found_map_set_elevator">未检测到地图,请检查是否完成梯控设置</string>
    <string name="text_can_not_find_point">找不到%1$s点位,任务结束</string>
    <string name="text_detect_not_use_default_map">检测到机器当前地图并不是默认楼层的地图,是否需要切换地图?</string>
    <string name="text_push_robot_to_elevator_out">请将机器人推到电梯前朝向电梯后点击确认按钮选择地图</string>
    <string name="text_select_disinfection_floor">请选择消毒楼层</string>
    <string name="text_default_map">充电桩楼层</string>
    <string name="text_choose">选择</string>
    <string name="text_can_not_find_map">找不到地图</string>
    <string name="text_choose_floor">请选择楼层</string>
    <string name="elevator_out_point">电梯口</string>
    <string name="elevator_in_point">电梯内</string>
    <string name="text_elevator_setting">梯控设置</string>
    <string name="text_main_control_ip">主控制器</string>
    <string name="text_port_map">端口映射</string>
    <string name="text_please_choose_floor">请根据端口选择对应楼层</string>
    <string name="text_please_input_floor">请输入楼层</string>
    <string name="text_port">端口:</string>
    <string name="text_floor">楼层:</string>
    <string name="text_please_input_port">请输入端口</string>
    <string name="text_delete_port">删除</string>
    <string name="text_port_or_floor_can_not_same">端口或楼层不能重复</string>
    <string name="text_map_name_authorized">地图名称不合法,请修改机器人地图名称</string>
    <string name="text_call_elevator_success">预约电梯成功</string>
    <string name="text_elevator_arrive">电梯到达,正在导航去电梯,请注意避让</string>
    <string name="text_arrive_inside">到达电梯内,正在前往%1$d</string>
    <string name="text_arrive_target_floor">到达%1$s,正在切换地图中...</string>
    <string name="text_going_to_elevator_out">正在前往电梯口,请注意避让</string>
    <string name="text_open_door_fail">开门失败,任务结束</string>
    <string name="text_go_to_target_floor_fail">前往%1$d楼失败</string>
    <string name="text_call_elevator_fail">呼梯失败,任务结束</string>
    <string name="text_get_elevator_fail">获取电梯状态失败,任务结束</string>
    <string name="voice_not_guide_set_elevator">检测到您是第一次使用,是否设置梯控信息</string>
    <string name="text_selected_floor">已选楼层</string>
    <string name="text_out_control_ip">外呼控制器</string>
    <string name="text_choose_floors_first">请先选择好楼层再开始任务</string>
    <string name="text_not_set_port_mapping">检测到未设置楼层端口映射,无法开始任务</string>
    <string name="text_now_floor">电梯在%1$d层</string>
    <string name="text_log">日志上传</string>
    <string name="text_upload">上传</string>
    <string name="text_init_elevator_io_exception">初始化梯控失败,IO异常</string>
    <string name="text_init_elevator_ip_or_port_null">初始化梯控失败,请检查梯控IP和端口设置</string>
    <string name="text_wifi_not_connect_elevator_error">网络断开,乘梯失败</string>

    <string name="text_elevator_io_error">梯控模块读写异常: %1$s</string>
    <string name="text_error14">%1$s 电梯测距模块异常，10分钟未到达</string>
    <string name="text_disorder">%1$s 指令发送步骤异常</string>
    <string name="text_error01">%1$s 呼叫楼层错误，当前楼层：%2$d，目标楼层: %3$d</string>
    <string name="text_error04">%1$s 指令发送步骤异常，没有权限</string>
    <string name="text_error05">%1$s 平层错误</string>
    <string name="text_error11">%1$s 机器人长时间没有发任务,权限被取消</string>
    <string name="text_error12">%1$s 电梯到达目标楼层，机器人长时间不回复消息</string>
    <string name="text_error13">%1$s 电梯开门超时，超时前没有收到关门消息</string>
    <string name="text_serial_port_not_found">未检测到通信板串口，请确保通信板正确安装后重新进入应用</string>
    <string name="error_dd">电梯收到正服务的机器人逻辑顺序混乱或错误的指令参数</string>
    <string name="error_de">电梯收到正服务的机器人未定义的指令参数</string>
    <string name="error_df">电梯故障升级或已被其他机器人占用</string>
    <string name="error_parameter_error">参数错误</string>
    <string name="error_request_error">请求超时</string>
    <string name="text_request_elevator_permission_failed">申请电梯权限失败，请尝试重新进入应用或联系技术人员</string>
    <string name="elevator_port_open_failed">梯控通信端口打开异常，请尝试重新进入应用或联系技术人员</string>
    <string name="text_floor_level_tip">楼层高度</string>
    <string name="text_floor_level_low">低于7层</string>
    <string name="text_floor_level_high">高于7层</string>
    <string name="text_permission_success">获取电梯权限成功</string>
    <string name="text_reloc">重定位</string>
    <string name="text_reloc_in_point">请将机器推到[充电桩]或[电梯口]点击指定按钮进行重定位</string>


    <string-array name="speed">
        <item>0.1</item>
        <item>0.2</item>
        <item>0.3</item>
        <item>0.4</item>
        <item>0.5</item>
        <item>0.6</item>
        <item>0.7</item>
        <item>0.8</item>
    </string-array>


    <string-array name="languages">
        <item>English</item>
        <item>中文(简体)</item>
        <item>日本語</item>
        <item>한국인</item>
    </string-array>

    <string-array name="ports">
        <item>3964</item>
        <item>3965</item>
    </string-array>

</resources>