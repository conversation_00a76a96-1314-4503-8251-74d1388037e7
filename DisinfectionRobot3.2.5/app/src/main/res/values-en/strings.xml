<?xml version="1.0" encoding="utf-8"?>
<resources>

    <string name="strNetworkTipsCancelBtn">Cancel</string>
    <string name="strNetworkTipsConfirmBtn">Continue</string>
    <string name="strNetworkTipsMessage">The Network have switch to mobile network, continue？</string>
    <string name="strNetworkTipsTitle">Network tips</string>
    <string name="strNotificationClickToContinue">Continue download</string>
    <string name="strNotificationClickToInstall">Click to install</string>
    <string name="strNotificationClickToRetry">Click to retry</string>
    <string name="strNotificationClickToView">Click to check</string>
    <string name="strNotificationDownloadError">Download fail</string>
    <string name="strNotificationDownloadSucc">Download finished</string>
    <string name="strNotificationDownloading">Downloading...</string>
    <string name="strNotificationHaveNewVersion">Have new version</string>
    <string name="strToastCheckUpgradeError">Check upgrade fail, retry after</string>
    <string name="strToastCheckingUpgrade">Checking upgrade...</string>
    <string name="strToastYourAreTheLatestVersion">You have no new version</string>
    <string name="strUpgradeDialogCancelBtn">Next time</string>
    <string name="strUpgradeDialogContinueBtn">Continue</string>
    <string name="strUpgradeDialogFeatureLabel">Upgrade feature</string>
    <string name="strUpgradeDialogFileSizeLabel">Package Size</string>
    <string name="strUpgradeDialogInstallBtn">Install</string>
    <string name="strUpgradeDialogRetryBtn">Retry</string>
    <string name="strUpgradeDialogUpdateTimeLabel">Upgrade time</string>
    <string name="strUpgradeDialogUpgradeBtn">Upgrade</string>
    <string name="strUpgradeDialogVersionLabel">Version</string>

    <string name="pickerview_cancel">cancel</string>
    <string name="pickerview_submit">confirm</string>

    <string name="text_app_name">DisinfectionRobot-Elevator</string>
    <string name="text_charging_pile">charging_pile</string>
    <string name="text_choose_map">Select map</string>
    <string name="text_title_recent_task">Recent task today</string>
    <string name="text_title_robot_state">Machine status</string>
    <string name="text_look_up_recent_task">view recent tasks</string>
    <string name="text_go_to_charge">charge</string>
    <string name="text_title_manual_task">Manual disinfection mode</string>
    <string name="text_start_disinfection">Start disinfection</string>
    <string name="text_lock_screen">lock screen</string>
    <string name="text_choose_wifi">WiFi settings</string>
    <string name="text_build_map">Map deployment</string>
    <string name="text_current_map">Current Map</string>
    <string name="text_current_wifi">ROS WiFi</string>
    <string name="text_current_ip">ROS IP</string>
    <string name="text_title_task_setting">Task settings</string>
    <string name="text_back">Back</string>
    <string name="text_single_mode">single execution</string>
    <string name="text_duration_mode">duration cycle</string>
    <string name="text_destination_when_task_complete">Disinfection completed</string>
    <string name="text_go_to_starting_point_when_task_finished">starting point</string>
    <string name="text_disinfection_switch">Disinfection switch</string>
    <string name="text_open_all_the_way">full open</string>
    <string name="text_open_in_target_point">open at target point</string>
    <string name="text_close_all_the_way">full close</string>
    <string name="text_stay_time">Residence time at target point</string>
    <string name="text_duration_time">Cycle duration</string>
    <string name="text_cycle_mode">Disinfection mode</string>
    <string name="text_test_result_charging_docking_failed">Charging docking failed</string>
    <string name="text_test_result_charging_docking_success">Charging docking successful</string>
    <string name="text_route_test_failed">Disinfection route test failed, failed to arrive %1$s</string>
    <string name="text_route_test_success">Disinfection route test succeeded</string>
    <string name="text_invalid_time_in_duration_mode">Illegal cycle duration (greater than 5 seconds)</string>
    <string name="text_open_in_target_point_when_stay_time_is_0">When the dwell time is 0, do not set the disinfection switch to open at the target point</string>
    <string name="text_title_system_setting">System settings</string>
    <string name="text_account">Current account</string>
    <string name="text_not_login">Not logged in</string>
    <string name="text_choice_voice_broadcast">Voice broadcast</string>
    <string name="text_open">open</string>
    <string name="text_close">close</string>
    <string name="text_choice_lock_screen">Lock screen</string>
    <string name="text_choice_speed">Running speed</string>
    <string name="text_low_power">Low charge</string>
    <string name="text_choice_volume">Volume control</string>
    <string name="text_delay_disinfection">Task countdown</string>
    <string name="text_second">sec</string>
    <string name="text_current_version">Current version</string>
    <string name="text_ros_wifi_connect_failed">Navigation WiFi connection failed</string>
    <string name="text_android_wifi_connect_failed">Android network connection failed</string>
    <string name="text_wifi_name_can_not_be_empty">WiFi name cannot be empty</string>
    <string name="text_speed_unit">m/s</string>
    <string name="text_is_login">Signing in, please wait...</string>
    <string name="text_task_already_update">Tasks synchronized</string>
    <string name="text_task_saved_success">Task saved successfully</string>
    <string name="text_saving_task">Saving task, please wait...</string>
    <string name="text_language_setting">Current language</string>
    <string name="text_finish">complete</string>
    <string name="text_please_choose_map">Please select a map</string>
    <string name="text_loading_map_list">Loading maps, please wait...</string>
    <string name="text_changing_map">Switching maps, please wait...</string>
    <string name="text_change_map_success">Map switching succeeded</string>
    <string name="text_map_load_failed">Map loading failed</string>
    <string name="text_wifi_not_connect">Not connected</string>
    <string name="text_delete"><![CDATA[<]]></string>
    <string name="text_please_enter_lock_password">Please enter the lock screen password</string>
    <string name="text_exit_login">Log out</string>
    <string name="text_login">Sign in</string>
    <string name="text_login_success">Login succeeded</string>
    <string name="text_login_failed">Login failed</string>
    <string name="text_username_can_not_be_empty">User name cannot be empty</string>
    <string name="text_please_enter_password">Please input password</string>
    <string name="text_skip">Skip</string>
    <string name="text_please_enter_username">Please input username</string>
    <string name="text_ultraviolet_disinfection_robot">Ultraviolet disinfection robot</string>
    <string name="text_atomization_disinfection_robot">Atomization disinfection robot</string>
    <string name="text_robot_type_choose">Model selection</string>
    <string name="text_restart_for_configuration_change">The application configuration has changed and will re-enter the application</string>
    <string name="text_next_step">next step</string>
    <string name="text_enter_app">Enter application</string>
    <string name="text_exit_app">Exit application</string>
    <string name="text_invalid_low_power">Illegal automatic charging power (10 ~ 80)</string>
    <string name="text_choose_speed">Select running speed (m/s)</string>
    <string name="text_title_scheduled_task_list">Schedule task list</string>
    <string name="text_task_name">Task name</string>
    <string name="text_repeat_date">Repeat date</string>
    <string name="text_repeat_monday">Mon</string>
    <string name="text_repeat_sunday">Sun</string>
    <string name="text_repeat_saturday">Sat</string>
    <string name="text_repeat_friday">Fri</string>
    <string name="text_repeat_thursday">Thu</string>
    <string name="text_repeat_wednesday">Wed</string>
    <string name="text_repeat_tuesday">Tue</string>
    <string name="text_start_time">Start time</string>
    <string name="text_invalid_task_name">Task name cannot be empty</string>
    <string name="text_default_scheduled_task_name">schedule task%1$s</string>
    <string name="text_create_disinfection_task">Create disinfection task</string>
    <string name="text_modify_disinfection_task">Modify disinfection task</string>
    <string name="text_save">Save</string>
    <string name="text_create">Create</string>
    <string name="text_task_enabled">task enabled</string>
    <string name="text_task_disabled">task disabled</string>
    <string name="text_date_format">HH:mm:ss</string>
    <string name="text_invalid_repeat_time">Please select a working day</string>
    <string name="text_enter_lock_screen">Please enter the lock screen password</string>
    <string name="text_cancel">cancel</string>
    <string name="text_confirm">confirm</string>

    <string name="text_test_result_canceled_by_user">The test task has been manually cancelled</string>
    <string name="text_test_result_success">The disinfection point test is successful, and the charging docking test is successful</string>
    <string name="text_test_result_charger_not_found">Charging docking failed, charging  not found</string>
    <string name="text_test_failed_for_not_found_start_point">Disinfection route test failed, disinfection starting point not found</string>
    <string name="text_going_to_charging_pile_for_charge_test">Going to the charging pile for charging docking test</string>
    <string name="text_task_was_canceled">The task has been cancelled</string>
    <string name="text_going_to_start_point">Returning to starting point</string>
    <string name="text_going_to_charge">Going to charging pile to charge</string>
    <string name="text_going_to_start_task_in_future">The task will begin in %1$d seconds</string>
    <string name="text_navigating_to_target_point_for_test">Going to disinfection point %1$s for test</string>
    <string name="text_confirm_delete_this_task">Are you sure to delete the current scheduled task?</string>
    <string name="text_going_to_exec_schedule_task">The scheduled task [%2$s] will start in %1$d seconds</string>
    <string name="text_going_to_charge_for_low_power">The battery is too low. I will go to the charging pile to charge in %1$d seconds</string>
    <string name="text_connect_failed">Network connection failed</string>
    <string name="text_connect_success">Network connection succeeded</string>
    <string name="text_connect_time_out">Network connection timeout</string>
    <string name="text_wifi_password_can_not_be_empty">Password cannot be empty</string>
    <string name="text_auth">authentication</string>
    <string name="text_wifi_password">Wifi password</string>
    <string name="text_wifi_name">WiFi name</string>
    <string name="text_wifi_auth">WiFi authentication</string>
    <string name="text_already_updated">Updated</string>
    <string name="text_current_ros_wifi">Navigation: %1$s</string>
    <string name="text_open_wifi_first">Please open WiFi first</string>
    <string name="text_current_android_wifi">Android: %1$s</string>
    <string name="text_wifi_disabling">Closing</string>
    <string name="text_wifi_enabling">Opening</string>
    <string name="text_opened">Opened</string>
    <string name="text_closed">Closed</string>
    <string name="text_enter_lock_screen_password">Enter the lock screen password</string>
    <string name="text_password_error">Password error, please re-enter</string>
    <string name="text_enter_lock_screen_password_again">Enter the lock screen password again</string>
    <string name="text_invalid_lock_screen_password">Illegal lock screen password (4 digits)</string>
    <string name="text_passwords_are_not_same">The two passwords are inconsistent, please re-enter</string>
    <string name="text_rebuild_map">Rebuild map</string>
    <string name="text_set_disinfection_point">Add disinfection point</string>
    <string name="text_set_charging_pile">Set as charging pile</string>
    <string name="text_exit_deploy">Exit deployment</string>
    <string name="text_save_map">Save map</string>
    <string name="text_back_to_main_page">Return to main page</string>
    <string name="text_disinfection_point_test">Route test</string>
    <string name="text_current_host_name">ROS name: %1$s</string>
    <string name="text_dialog_title_prompt">Tips</string>
    <string name="text_emergency_stop_turn_on">Emergency stop on</string>
    <string name="text_charging">The robot is charging</string>
    <string name="text_low_pow">Low power of robot</string>
    <string name="text_title_task_state">Disinfection status</string>
    <string name="text_current_task_type">Task type: %1$s</string>
    <string name="text_current_task_mode">Disinfection mode: %1$s</string>
    <string name="text_current_task_start_time">Start time: %1$s</string>
    <string name="text_current_task_time_waste">"Disinfection duration: %1$s "</string>
    <string name="text_current_disinfection_switch_state">Disinfection switch: %1$s</string>
    <string name="text_current_destination">Going to: %1$s</string>
    <string name="text_current_task_time_remain">Time remaining: %1$s</string>
    <string name="text_finish_task">Terminate</string>
    <string name="text_pause_task">Suspend</string>
    <string name="text_manual_task">Manual task</string>
    <string name="text_scheduled_task">scheduled tasks</string>
    <string name="text_confirm_add_disinfection_point_at_this_location">Are you sure to set the current position as disinfection point %1$d?</string>
    <string name="text_time_duration_format">%02d:%02d:%02d</string>
    <string name="text_time_duration_format2">%02d:%02d:%02d</string>
    <string name="text_can_not_find_starting_point">The disinfection starting point is not found. Please check whether the disinfection point is marked</string>
    <string name="text_resume_task">resume task</string>
    <string name="text_navigation_version">Navigation version</string>
    <string name="text_synthesize_audio">audition</string>
    <string name="text_save_success">Saved successfully</string>
    <string name="text_disinfection_voice">Voice in disinfection</string>
    <string name="text_not_found_audio_file">No relevant audio files found, please synthesize audio first</string>
    <string name="text_input_disinfection_prompt_first">Please input the text corresponding to the synthesized audio first</string>
    <string name="text_page_load_failed">Web page loading timeout, please ensure that Android and navigation host are connected to the same network</string>
    <string name="text_ros_wifi">ROS WiFi: %1$s</string>
    <string name="text_ros_ip">ROS IP: %1$s</string>
    <string name="text_open_wechat">Step 1:\n please open wechat and scan the QR code below to log in to your robot account</string>
    <string name="text_scan_code_to_bind_machine">Step 2:\n please use wechat applet to scan the QR code below to bind the machine</string>
    <string name="text_wechat_app">Bind account</string>
    <string name="text_click_to_bind">Click to bind account</string>
    <string name="text_save_task">Save task</string>
    <string name="text_switch">switch</string>
    <string name="text_invalid_delay_time">Please enter the delay disinfection duration</string>
    <string name="text_logout_success">Logged out</string>
    <string name="text_start_right_now">start now</string>
    <string name="text_cancel_task">cancel task</string>
    <string name="text_do_not_apply_map_repeatedly">Do not switch maps repeatedly</string>
    <string name="text_synthesize_error">Audio generation failed: %1$s</string>

    <string name="exception_chassis_failure">Chassis fault, please contact technical personnel.</string>
    <string name="exception_chassis_over_current">Chassis over current, please contact technical personnel.</string>
    <string name="exception_encounter_obstacle_in_target_point">There are obstacles in the target point, task failed.</string>
    <string name="exception_encounter_obstacle_in_target_point_or_in_the_way">Navigation failed due to obstacles on the way or at the target point.</string>
    <string name="exception_encounter_obstacle_in_the_way">Encountered obstacles during navigation, task failed.</string>
    <string name="exception_forbidden_zone">Enter the restricted area, task failed.</string>
    <string name="exception_imu_error">IMU fault, please contact technical personnel.</string>
    <string name="exception_laser_error">Laser abnormal, please contact technical personnel.</string>
    <string name="exception_new_target">New navigation target, navigation failed.</string>
    <string name="exception_not_in_work_space">Currently out of workspace,  task failed.</string>
    <string name="exception_remote_cancel_navigation">The current task has been cancelled</string>
    <string name="exception_speedometer_error">The odometer is abnormal, please contact the technician.</string>
    <string name="exception_tripped_in_obstacle">Falling into an obstacle or too close to a wall, task failed.</string>
    <string name="exception_visual_mark">Abnormal label positioning</string>

    <string name="voice_low_power_go_to_charge">The battery is too low. I\'m going to charge it</string>
    <string name="voice_stop_charge">Stop charging</string>
    <string name="voice_route_test_finished">Disinfection route test completed</string>
    <string name="voice_charging_dock_failed">Automatic charging failed</string>
    <string name="voice_charging_docking_failed_and_re_dock">Charging docking failed, trying to re dock</string>
    <string name="voice_not_found_charging_pile">Charging pile not found</string>
    <string name="voice_not_guide_build_map">It is detected that you are entering for the first time. Do you need me to help you deploy the map?</string>
    <string name="voice_guide_goto_setting">Click here to enter the setting interface.</string>
    <string name="voice_guide_exit_app">Quickly click here twice, and then slide up from the bottom of the screen to exit the application</string>
    <string name="voice_guide_goto_charge">Click here to issue charging instruction.</string>
    <string name="voice_guide_look_up_recent_task">Click here to view scheduled tasks.</string>
    <string name="voice_guide_start_disinfection">Click here to start disinfection task.</string>
    <string name="voice_guide_goto_manual_task">Click here to set manual tasks.</string>
    <string name="voice_guide_finish">Congratulations on completing the operation guidance!</string>
    <string name="voice_guide_more_actions">Slide from the right side of the screen to the left for more operations.</string>
    <string name="voice_going_to_charging">OK, let\'s charge it now</string>
    <string name="voice_already_in_charging">I\'m charging</string>
    <string name="voice_task_finished">Mission accomplished</string>
    <string name="voice_click_to_rebuild_map">Rebuilding the map will discard the currently scanned map and rescan the map. Please confirm your operation.</string>
    <string name="voice_entering_map_building_mode">Entering map-building mode, please wait...</string>
    <string name="voice_entering_nav_mode">Entering navigation mode, please wait...</string>
    <string name="voice_start_charging">Start charging</string>
    <string name="voice_start_docking_charging_pile">Reach the charging area and start docking the charging pile</string>
    <string name="voice_task_finish_and_go_to_start_point">The task is completed. I\'m going back to my starting point</string>
    <string name="voice_task_finish_and_go_to_charge">The task is completed. I\'m going to charge.</string>
    <string name="voice_stay_away_from_me">Disinfection is in progress. Please don\'t come near me</string>
    <string name="voice_going_to_start_disinfection_task">I\'m going to start disinfection</string>
    <string name="voice_cut_off_power_to_start_task">Please disconnect the power adapter before starting the task</string>
    <string name="voice_turn_off_scram_stop_to_start_task">Please turn off the emergency stop switch before starting the task.</string>
    <string name="voice_emergency_stop_turn_on">My emergency stop switch has been pressed and can\'t be moved</string>
    <string name="voice_going_to_start_disinfection_route_test">The disinfection route test is about to start. The disinfection switch will not be turned on in the whole process. Please ensure that the disinfection point and charging pile have been set.</string>
    <string name="voice_confirm_exit_deploy">Are you sure to exit map deployment? Please ensure that the disinfection point and charging point have been set, and the disinfection route test has been successfully carried out.</string>
    <string name="voice_target_point_mark_success">The target point is set successfully</string>
    <string name="voice_target_point_mark_failed">Failed to set target point</string>
    <string name="voice_current_in_nav_mode">It is currently in navigation mode. You can set disinfection points and test disinfection routes.</string>
    <string name="voice_current_in_map_building_mode">Currently in map building mode, please press the emergency stop switch, and then push the robot to start scanning the map.</string>
    <string name="voice_click_to_abandon_map_building">Returning to the main page will not save the currently scanned map. Please confirm your operation.</string>
    <string name="voice_click_to_save_map">You are about to save the map and exit the map-building mode. Please confirm your operation.</string>
    <string name="voice_saving_map_and_entering_nav_mode">Saving map and entering navigation mode, please wait...</string>
    <string name="voice_confirm_set_charging_pile_at_this_location">Please ensure that the robot is correctly docked on the charging pile, and then click confirm to set the current position as the charging pile.</string>
    <string name="voice_confirm_add_disinfection_point_at_this_location">Are you sure to set the current position as the disinfection point?</string>
    <string name="voice_wifi_connect_success">Successfully connected to the network</string>
    <string name="voice_please_login">Log in</string>
    <string name="voice_please_connect_wifi">Configure network</string>
    <string name="voice_select_robot_type">Select robot type</string>
    <string name="voice_please_enter_app">Please enter the application</string>
    <string name="voice_login_success">Login Successful</string>
    <string name="voice_connecting_wifi">Connecting to the network, please wait...</string>
</resources>