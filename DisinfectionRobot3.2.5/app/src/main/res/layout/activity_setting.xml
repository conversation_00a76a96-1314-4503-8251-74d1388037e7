<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_main_background"
    android:orientation="vertical"
    android:paddingLeft="40dp"
    android:paddingTop="@dimen/pd_sm"
    android:paddingRight="40dp"
    android:paddingBottom="@dimen/pd_xl"
    tools:context=".activities.TaskCreateActivity">


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/pd_sm"
        android:paddingBottom="@dimen/pd_sm">

        <TextView
            android:id="@+id/tv_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableStart="@drawable/ic_back"
            android:padding="@dimen/pd_sm"
            android:text="@string/text_back"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_xl" />

        <TextView
            android:id="@+id/tv_sys_setting"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="@string/text_title_system_setting"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_common"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_logout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:padding="@dimen/pd_sm"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_xl" />

    </RelativeLayout>


    <ScrollView
        android:id="@+id/sv_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/img_content_background"
        android:overScrollMode="never"
        android:padding="@dimen/pd_xl"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:paddingTop="@dimen/pd_sm"
                android:paddingBottom="@dimen/pd_sm">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/text_account"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_common" />

                <TextView
                    android:id="@+id/tv_current_username"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/mr_lg"
                    android:text="@string/text_not_login"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_common" />


            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingTop="@dimen/pd_sm"
                android:paddingBottom="@dimen/pd_sm">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/text_language_setting"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_common" />

                <TextView
                    android:id="@+id/tv_current_language"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/mr_lg"
                    android:autoLink="all"
                    android:textAllCaps="false"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_common" />

                <Button
                    android:id="@+id/btn_switch_language"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/mr_md"
                    android:background="@drawable/selector_common_button"
                    android:text="@string/text_switch"
                    android:textAllCaps="false"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_lg" />


            </LinearLayout>

            <LinearLayout
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:gravity="center_vertical"
                android:paddingTop="@dimen/pd_xs"
                android:paddingBottom="@dimen/pd_xs">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/text_gating_switch"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_common" />

                <RadioGroup
                    android:id="@+id/rg_gating_switch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/mr_lg"
                    android:orientation="horizontal"
                    >
                    <RadioButton
                        android:id="@+id/rb_gating_open"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:buttonTint="@android:color/white"
                        android:text="@string/text_open"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_common"

                        />

                    <RadioButton
                        android:id="@+id/rb_gating_close"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:buttonTint="@android:color/white"
                        android:layout_marginStart="@dimen/mr_md"
                        android:checked="true"
                        android:text="@string/text_close"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_common"

                        />
                </RadioGroup>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_gating_port"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:gravity="center_vertical"
                android:paddingTop="@dimen/pd_xs"
                android:paddingBottom="@dimen/pd_xs">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/text_gating_serial_port"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_common" />

                <Spinner
                    android:id="@+id/sp_gating_serial_port"
                    android:layout_width="300dp"
                    android:background="#39A6FD"
                    android:layout_marginStart="@dimen/mr_lg"
                    android:layout_height="wrap_content" />

            </LinearLayout>

            <LinearLayout
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:gravity="center_vertical"
                android:paddingTop="@dimen/pd_xs"
                android:paddingBottom="@dimen/pd_xs">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/text_elevator_switch"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_common" />

                <RadioGroup
                    android:id="@+id/rg_elevator_switch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/mr_lg"
                    android:orientation="horizontal"
                    >
                    <RadioButton
                        android:id="@+id/rb_elevator_open"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:buttonTint="@android:color/white"
                        android:text="@string/text_open"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_common"
                        />

                    <RadioButton
                        android:id="@+id/rb_elevator_close"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:buttonTint="@android:color/white"
                        android:checked="true"
                        android:layout_marginStart="@dimen/mr_md"
                        android:text="@string/text_close"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_common"

                        />
                </RadioGroup>

                <Button
                    android:id="@+id/btn_elevator_setting"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/mr_md"
                    android:background="@drawable/selector_common_button"
                    android:text="@string/text_elevator_setting"
                    android:textAllCaps="false"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_lg" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_elevator_port"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:gravity="center_vertical"
                android:paddingTop="@dimen/pd_xs"
                android:paddingBottom="@dimen/pd_xs">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/text_elevator_serial_port"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_common" />


                <Spinner
                    android:id="@+id/sp_elevator_serial_port"
                    android:layout_width="300dp"
                    android:background="#39A6FD"
                    android:layout_marginStart="@dimen/mr_lg"
                    android:layout_height="wrap_content" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:gravity="center_vertical"
                android:paddingTop="@dimen/pd_xs"
                android:paddingBottom="@dimen/pd_xs">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/text_delay_disinfection"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_common" />

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:suffixText="@string/text_second"
                    app:suffixTextAppearance="@style/TextAppearance.AppCompat.Title"
                    app:suffixTextColor="@color/white">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_delay_time"
                        android:layout_width="200dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/mr_lg"
                        android:gravity="center"
                        android:inputType="number"
                        android:maxLength="4"
                        android:text="10"
                        android:textAlignment="center"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_common"
                        android:theme="@style/EditTextStyle" />

                </com.google.android.material.textfield.TextInputLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:paddingTop="@dimen/pd_sm"
                android:paddingBottom="@dimen/pd_sm">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/text_choice_lock_screen"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_common" />

                <RadioGroup
                    android:id="@+id/rg_lock_screen"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/mr_lg"
                    android:orientation="horizontal">

                    <RadioButton
                        android:id="@+id/rb_open_lock_screen"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:buttonTint="@android:color/white"
                        android:text="@string/text_open"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_common" />

                    <RadioButton
                        android:id="@+id/rb_close_lock_screen"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/mr_md"
                        android:buttonTint="@android:color/white"
                        android:text="@string/text_close"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_common" />

                </RadioGroup>


            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:paddingTop="@dimen/pd_sm"
                android:paddingBottom="@dimen/pd_sm">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/text_choice_voice_broadcast"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_common" />

                <RadioGroup
                    android:id="@+id/rg_voice_broadcast"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/mr_lg"
                    android:orientation="horizontal">

                    <RadioButton
                        android:id="@+id/rb_open_voice_broadcast"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:buttonTint="@android:color/white"
                        android:text="@string/text_open"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_common" />

                    <RadioButton
                        android:id="@+id/rb_close_voice_broadcast"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/mr_md"
                        android:buttonTint="@android:color/white"
                        android:text="@string/text_close"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_common" />
                </RadioGroup>


            </LinearLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:paddingTop="@dimen/pd_sm"
                android:paddingBottom="@dimen/pd_sm">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/text_choice_speed"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_common" />

                <TextView
                    android:id="@+id/tv_speed"
                    android:layout_width="140dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/mr_lg"
                    android:drawableEnd="@drawable/icon_drop_down"
                    android:drawablePadding="@dimen/pd_sm"
                    android:gravity="center"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_common" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:paddingTop="@dimen/pd_sm"
                android:paddingBottom="@dimen/pd_sm">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/text_wechat_app"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_common" />

                <TextView
                    android:id="@+id/tv_bind_robot"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/mr_lg"
                    android:gravity="center"
                    android:text="@string/text_click_to_bind"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_common" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_disinfection_prompt"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:gravity="center_vertical"
                android:paddingTop="@dimen/pd_xs"
                android:paddingBottom="@dimen/pd_xs">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/text_disinfection_voice"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_common" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:descendantFocusability="beforeDescendants"
                    android:focusable="true"
                    android:focusableInTouchMode="true">

                    <EditText
                        android:id="@+id/et_disinfection_prompt"
                        android:layout_width="320dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/mr_lg"
                        android:scrollHorizontally="true"
                        android:singleLine="true"
                        android:textAlignment="center"
                        android:textColor="@android:color/white"
                        android:textColorHint="@color/white"
                        android:textSize="@dimen/ts_common"
                        android:theme="@style/EditTextStyle" />
                </LinearLayout>


                <Button
                    android:id="@+id/btn_try_synthesize"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/mr_md"
                    android:background="@drawable/selector_common_button"
                    android:padding="@dimen/pd_xs"
                    android:text="@string/text_synthesize_audio"
                    android:textAllCaps="false"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_lg" />

                <Button
                    android:id="@+id/btn_save"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/mr_md"
                    android:background="@drawable/bg_common_button_inactive"
                    android:padding="@dimen/pd_xs"
                    android:text="@string/text_save"
                    android:textAllCaps="false"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_lg" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:paddingTop="@dimen/pd_sm"
                android:paddingBottom="@dimen/pd_sm">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/text_low_power"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_common" />

                <ImageButton
                    android:id="@+id/ib_decrease_power"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/mr_lg"
                    android:background="@null"
                    android:src="@drawable/icon_left" />

                <SeekBar
                    android:id="@+id/sb_low_power"
                    android:layout_width="220dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/mr_sm"
                    android:layout_marginEnd="@dimen/mr_sm"
                    android:max="100"
                    android:progress="20"
                    android:progressBackgroundTint="@color/white"
                    android:progressTint="@android:color/white"
                    android:thumbTint="@color/white" />

                <ImageButton
                    android:id="@+id/ib_raise_power"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@null"
                    android:src="@drawable/icon_right" />

                <TextView
                    android:id="@+id/tv_low_power"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_common" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_duration_time"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:paddingTop="@dimen/pd_xs"
                android:paddingBottom="@dimen/pd_xs">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/text_choice_volume"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_common" />

                <ImageButton
                    android:id="@+id/ib_decrease_volume"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/mr_lg"
                    android:background="@null"
                    android:src="@drawable/icon_left" />

                <SeekBar
                    android:id="@+id/sb_system_volume"
                    android:layout_width="220dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/mr_sm"
                    android:layout_marginEnd="@dimen/mr_sm"
                    android:max="100"
                    android:progress="20"
                    android:progressBackgroundTint="@color/white"
                    android:progressTint="@android:color/white"
                    android:thumbTint="@color/white" />

                <ImageButton
                    android:id="@+id/ib_raise_volume"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@null"
                    android:src="@drawable/icon_right" />

                <TextView
                    android:id="@+id/tv_sys_volume"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_common" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:gravity="center_vertical"
                android:paddingTop="@dimen/pd_xs"
                android:paddingBottom="@dimen/pd_xs">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/text_current_version"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_common" />

                <TextView
                    android:id="@+id/tv_current_version"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/mr_lg"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_common" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:gravity="center_vertical"
                android:paddingTop="@dimen/pd_xs"
                android:paddingBottom="@dimen/pd_xs">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/text_log"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_common" />

                <Button
                    android:id="@+id/btn_upload"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/mr_md"
                    android:background="@drawable/selector_common_button"
                    android:text="@string/text_upload"
                    android:textAllCaps="false"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_lg" />


            </LinearLayout>


        </LinearLayout>

    </ScrollView>


</LinearLayout>