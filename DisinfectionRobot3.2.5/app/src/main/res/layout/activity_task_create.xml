<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/color_main_background"
    android:orientation="vertical"
    android:paddingLeft="40dp"
    android:paddingTop="@dimen/pd_xs"
    android:paddingRight="40dp"
    android:paddingBottom="@dimen/pd_lg"
    tools:context=".activities.TaskCreateActivity">


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/pd_xs"
        android:paddingBottom="@dimen/pd_xs">

        <TextView
            android:id="@+id/tv_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableStart="@drawable/ic_back"
            android:padding="15dp"
            android:text="@string/text_back"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_xl" />

        <TextView
            android:id="@+id/tv_title_center"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="@string/text_title_task_setting"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_common"
            android:textStyle="bold" />


        <TextView
            android:id="@+id/tv_save"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:padding="15dp"
            android:text="@string/text_save"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_xl"
            android:visibility="invisible" />

    </RelativeLayout>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/img_content_background"
        android:orientation="vertical"
        android:paddingLeft="@dimen/mr_xl"
        android:paddingTop="@dimen/pd_md"
        android:paddingRight="@dimen/mr_xl"
        android:paddingBottom="@dimen/pd_md">

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">


                <LinearLayout
                    android:id="@+id/ll_task_start_time"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:focusable="true"
                    android:focusableInTouchMode="true"
                    android:gravity="center_vertical"
                    android:paddingBottom="@dimen/pd_xs">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/text_start_time"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_common" />

                    <TextView
                        android:id="@+id/tv_start_time"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/mr_lg"
                        android:paddingEnd="@dimen/pd_md"
                        android:textAlignment="center"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_xl" />


                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_task_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:focusable="true"
                    android:focusableInTouchMode="true"
                    android:gravity="center_vertical"
                    android:paddingBottom="@dimen/pd_xs">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/text_task_name"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_common" />

                    <EditText
                        android:id="@+id/et_task_name"
                        android:layout_width="300dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/mr_lg"
                        android:ellipsize="end"
                        android:inputType="textNoSuggestions"
                        android:singleLine="true"
                        android:textColor="@android:color/white"
                        android:textColorHint="@android:color/white"
                        android:textSize="@dimen/ts_xl"
                        android:theme="@style/EditTextStyle" />


                </LinearLayout>


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:visibility="gone"
                    android:paddingTop="@dimen/pd_xs"
                    android:paddingBottom="@dimen/pd_xs">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/text_cycle_mode"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_common" />

                    <RadioGroup
                        android:id="@+id/rg_cycle_mode"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/mr_lg"
                        android:orientation="horizontal">

                        <RadioButton
                            android:id="@+id/rb_single_mode"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:buttonTint="@android:color/white"
                            android:text="@string/text_single_mode"
                            android:textColor="@android:color/white"
                            android:textSize="@dimen/ts_common" />

                        <RadioButton
                            android:id="@+id/rb_duration_mode"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/mr_md"
                            android:buttonTint="@android:color/white"
                            android:text="@string/text_duration_mode"
                            android:textColor="@android:color/white"
                            android:textSize="@dimen/ts_common" />
                    </RadioGroup>


                </LinearLayout>

                <LinearLayout
                    android:visibility="gone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:paddingTop="@dimen/pd_xs"
                    android:paddingBottom="@dimen/pd_xs">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/text_destination_when_task_complete"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_common" />

                    <RadioGroup
                        android:id="@+id/rg_back_mode"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/mr_lg"
                        android:orientation="horizontal">

                        <RadioButton
                            android:id="@+id/rb_charge_when_finished"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:buttonTint="@android:color/white"
                            android:text="@string/text_go_to_charge"
                            android:textColor="@android:color/white"
                            android:textSize="@dimen/ts_common" />

                        <RadioButton
                            android:id="@+id/rb_back_when_finished"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/mr_md"
                            android:buttonTint="@android:color/white"
                            android:text="@string/text_go_to_starting_point_when_task_finished"
                            android:textColor="@android:color/white"
                            android:textSize="@dimen/ts_common" />
                    </RadioGroup>


                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:paddingTop="@dimen/pd_xs"
                    android:paddingBottom="@dimen/pd_xs">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/text_disinfection_switch"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_common" />

                    <RadioGroup
                        android:id="@+id/rg_switch_mode"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/mr_lg"
                        android:orientation="horizontal">

                        <RadioButton
                            android:id="@+id/rb_open_all_the_way"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:buttonTint="@android:color/white"
                            android:text="@string/text_open_all_the_way"
                            android:textColor="@android:color/white"
                            android:textSize="@dimen/ts_common" />

                        <RadioButton
                            android:id="@+id/rb_open_in_target_point"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/mr_md"
                            android:buttonTint="@android:color/white"
                            android:text="@string/text_open_in_target_point"
                            android:textColor="@android:color/white"
                            android:textSize="@dimen/ts_common" />

                        <RadioButton
                            android:id="@+id/rb_close_all_the_way"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/mr_md"
                            android:buttonTint="@android:color/white"
                            android:text="@string/text_close_all_the_way"
                            android:textColor="@android:color/white"
                            android:textSize="@dimen/ts_common" />
                    </RadioGroup>


                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_task_repeat_time"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:paddingTop="@dimen/pd_xs"
                    android:paddingBottom="2dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/text_repeat_date"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_common" />

                    <RadioGroup
                        android:id="@+id/rg_repeat_control"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/mr_lg"
                        android:orientation="horizontal">

                        <CheckBox
                            android:id="@+id/cb_repeat_monday"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:buttonTint="@android:color/white"
                            android:checked="true"
                            android:text="@string/text_repeat_monday"
                            android:textColor="@android:color/white"
                            android:textSize="@dimen/ts_common" />

                        <CheckBox
                            android:id="@+id/cb_repeat_tuesday"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/mr_md"
                            android:buttonTint="@android:color/white"
                            android:checked="true"
                            android:text="@string/text_repeat_tuesday"
                            android:textColor="@android:color/white"
                            android:textSize="@dimen/ts_common" />

                        <CheckBox
                            android:id="@+id/cb_repeat_wednesday"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/mr_md"
                            android:buttonTint="@android:color/white"
                            android:checked="true"
                            android:text="@string/text_repeat_wednesday"
                            android:textColor="@android:color/white"
                            android:textSize="@dimen/ts_common" />

                        <CheckBox
                            android:id="@+id/cb_repeat_thursday"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/mr_md"
                            android:buttonTint="@android:color/white"
                            android:checked="true"
                            android:text="@string/text_repeat_thursday"
                            android:textColor="@android:color/white"
                            android:textSize="@dimen/ts_common" />

                        <CheckBox
                            android:id="@+id/cb_repeat_friday"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/mr_md"
                            android:buttonTint="@android:color/white"
                            android:checked="true"
                            android:text="@string/text_repeat_friday"
                            android:textColor="@android:color/white"
                            android:textSize="@dimen/ts_common" />

                        <CheckBox
                            android:id="@+id/cb_repeat_saturday"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/mr_md"
                            android:buttonTint="@android:color/white"
                            android:checked="true"
                            android:text="@string/text_repeat_saturday"
                            android:textColor="@android:color/white"
                            android:textSize="@dimen/ts_common" />

                        <CheckBox
                            android:id="@+id/cb_repeat_sunday"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/mr_md"
                            android:buttonTint="@android:color/white"
                            android:checked="true"
                            android:text="@string/text_repeat_sunday"
                            android:textColor="@android:color/white"
                            android:textSize="@dimen/ts_common" />
                    </RadioGroup>


                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_stay_time"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:paddingTop="@dimen/pd_sm">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/text_stay_time"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_common" />

                    <TextView
                        android:id="@+id/tv_stay_time"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/mr_lg"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_common" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_duration_time"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:paddingTop="@dimen/pd_sm">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:autoLink="all"
                        android:text="@string/text_duration_time"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_common" />

                    <TextView
                        android:id="@+id/tv_duration_time"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/mr_lg"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_common" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_floor_choose"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingTop="@dimen/pd_sm">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:autoLink="all"
                        android:text="@string/text_floor_choose"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_common" />

                    <TextView
                        android:id="@+id/tv_not_found_floor"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/mr_lg"
                        android:visibility="gone"
                        android:textColor="#FF0000"
                        android:textSize="@dimen/ts_common" />

                    <com.donkingliang.labels.LabelsView
                        android:id="@+id/lb_floors"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/mr_lg"
                        app:drawableEnd="@drawable/cb_select"
                        app:isTextBold="true"
                        app:labelBackground="#00000000"
                        app:labelTextColor="@color/white"
                        app:labelTextSize="@dimen/ts_common"
                        app:maxLines="0"
                        app:maxSelect="0"
                        app:selectType="MULTI"
                        app:wordMargin="@dimen/mr_lg" />

                </LinearLayout>


            </LinearLayout>
        </ScrollView>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <Button
                android:id="@+id/btn_save"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/mr_sm"
                android:background="@drawable/bg_btn_start_task"
                android:paddingStart="30dp"
                android:paddingTop="@dimen/pd_md"
                android:paddingEnd="30dp"
                android:paddingBottom="@dimen/pd_md"
                android:text="@string/text_save_task"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:textSize="@dimen/ts_xl" />
        </RelativeLayout>

    </LinearLayout>


</LinearLayout>