<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/dl_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_main_background"
    tools:context=".activities.MainActivity">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:paddingLeft="@dimen/pd_xl"
        android:paddingRight="@dimen/pd_xl"
        android:paddingBottom="@dimen/pd_xl">

        <!--头部状态栏-->
        <LinearLayout
            android:id="@+id/rl_status_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:paddingLeft="@dimen/pd_sm"
            android:paddingTop="@dimen/pd_md"
            android:paddingRight="@dimen/pd_sm"
            android:paddingBottom="@dimen/pd_md">

            <TextView
                android:id="@+id/tv_hostname"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableStart="@drawable/img_robot_host_name"
                android:drawablePadding="@dimen/pd_xs"
                android:padding="@dimen/pd_xs"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_xl" />

            <View
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1" />

            <TextView
                android:id="@+id/tv_wifi_state"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableStart="@drawable/img_wifi"
                android:drawablePadding="@dimen/pd_xs"
                android:ellipsize="end"
                android:gravity="center"
                android:maxWidth="400dp"
                android:paddingLeft="@dimen/pd_md"
                android:paddingRight="@dimen/pd_md"
                android:scaleType="center"
                android:singleLine="true"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_xl" />

            <TextView
                android:id="@+id/tv_power"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawablePadding="@dimen/pd_sm"
                android:gravity="center"
                android:textAlignment="center"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_xl" />

            <ImageView
                android:id="@+id/iv_charging"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/mr_sm"
                android:src="@drawable/charging"
                android:visibility="gone" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <!--左侧-->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:orientation="vertical">

                <!--机器状态-->
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="280dp"
                    android:background="@drawable/img_robot_state_background"
                    android:orientation="vertical"
                    android:padding="@dimen/pd_md">

                    <TextView
                        android:id="@+id/tv_machine_state"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerHorizontal="true"
                        android:layout_gravity="center_horizontal"
                        android:text="@string/text_title_robot_state"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_common"
                        android:textStyle="bold" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="0dp"
                        android:layout_above="@id/ll_lock_and_charge"
                        android:layout_below="@id/tv_machine_state"
                        android:layout_centerHorizontal="true"
                        android:gravity="center_vertical"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tv_emergency_stop_state"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:drawableStart="@drawable/ic_warning"
                            android:drawablePadding="@dimen/pd_md"
                            android:gravity="center"
                            android:text="@string/text_emergency_stop_turn_on"
                            android:textColor="@android:color/white"
                            android:textSize="@dimen/ts_xl" />

                        <TextView
                            android:id="@+id/tv_charge_state"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/mr_sm"
                            android:drawablePadding="@dimen/pd_md"
                            android:gravity="center"
                            android:textColor="@android:color/white"
                            android:textSize="@dimen/ts_xl" />

                    </LinearLayout>


                    <LinearLayout
                        android:id="@+id/ll_lock_and_charge"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_alignParentBottom="true"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tv_lock"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:drawableTop="@drawable/img_lock"
                            android:drawablePadding="@dimen/pd_sm"
                            android:text="@string/text_lock_screen"
                            android:textAlignment="center"
                            android:textColor="@android:color/white"
                            android:textSize="@dimen/ts_lg" />

                        <TextView
                            android:id="@+id/tv_charge"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:drawableTop="@drawable/img_charge"
                            android:drawablePadding="@dimen/pd_sm"
                            android:text="@string/text_go_to_charge"
                            android:textAlignment="center"
                            android:textColor="@android:color/white"
                            android:textSize="@dimen/ts_lg" />
                    </LinearLayout>


                </RelativeLayout>


                <!--最近任务-->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="190dp"
                    android:layout_marginTop="@dimen/mr_xl"
                    android:background="@drawable/img_recent_task_background"
                    android:orientation="vertical"
                    android:padding="@dimen/pd_sm">

                    <TextView
                        android:id="@+id/tv_title_recent_task"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:text="@string/text_title_recent_task"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_common"
                        android:textStyle="bold" />

                    <RelativeLayout
                        android:id="@+id/rl_recent_scheduled_task"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_weight="1"
                        android:paddingLeft="@dimen/pd_md"
                        android:paddingRight="@dimen/pd_md">

                        <TextView
                            android:id="@+id/tv_task_name"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentStart="true"
                            android:layout_centerVertical="true"
                            android:drawableStart="@drawable/task_name"
                            android:drawablePadding="@dimen/pd_sm"
                            android:ellipsize="end"
                            android:gravity="center"
                            android:maxWidth="220dp"
                            android:singleLine="true"
                            android:textColor="@android:color/white"
                            android:textSize="22sp" />

                        <TextView
                            android:id="@+id/tv_task_time"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:drawableStart="@drawable/task_point_stop_time"
                            android:drawablePadding="@dimen/pd_sm"
                            android:gravity="center"
                            android:textColor="@android:color/white"
                            android:textSize="22sp" />

                    </RelativeLayout>

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/btn_look_up_scheduled_task"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal|bottom"
                        android:background="@null"
                        android:padding="@dimen/pd_md"
                        android:text="@string/text_look_up_recent_task"
                        android:textAllCaps="false"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_lg" />


                </LinearLayout>

            </LinearLayout>

            <!--右侧-->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginStart="@dimen/mr_xl"
                android:layout_weight="1.5"
                android:background="@drawable/img_manual_task_background"
                android:orientation="vertical"
                android:padding="@dimen/pd_md">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:text="@string/text_title_manual_task"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_common"
                    android:textStyle="bold" />


                <ScrollView
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:layout_gravity="center"
                    android:layout_weight="1">

                    <LinearLayout
                        android:id="@+id/ll_manual_task"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/mr_md"
                        android:layout_marginEnd="30dp"
                        android:layout_marginBottom="@dimen/mr_md"
                        android:gravity="center_vertical"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tv_task_mode"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:drawablePadding="10dp"
                            android:gravity="center"
                            android:paddingTop="@dimen/pd_sm"
                            android:paddingBottom="@dimen/pd_sm"
                            android:textColor="@android:color/white"
                            android:textSize="@dimen/ts_common"
                            />

                        <TextView
                            android:id="@+id/tv_back_action"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:drawablePadding="10dp"
                            android:gravity="center"
                            android:paddingTop="@dimen/pd_sm"
                            android:paddingBottom="@dimen/pd_sm"
                            android:textColor="@android:color/white"
                            android:textSize="@dimen/ts_common" />

                        <TextView
                            android:id="@+id/tv_disinfection_mode"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:drawablePadding="10dp"
                            android:gravity="center"
                            android:paddingTop="@dimen/pd_sm"
                            android:paddingBottom="@dimen/pd_sm"
                            android:textColor="@android:color/white"
                            android:textSize="@dimen/ts_common" />

                        <TextView
                            android:id="@+id/tv_stay_time"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:drawablePadding="10dp"
                            android:gravity="center"
                            android:paddingTop="@dimen/pd_sm"
                            android:paddingBottom="@dimen/pd_sm"
                            android:textColor="@android:color/white"
                            android:textSize="@dimen/ts_common" />

                        <TextView
                            android:id="@+id/tv_duration_time"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:drawablePadding="10dp"
                            android:gravity="center"
                            android:paddingTop="@dimen/pd_sm"
                            android:paddingBottom="@dimen/pd_sm"
                            android:textColor="@android:color/white"
                            android:textSize="@dimen/ts_common" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:drawablePadding="10dp"
                            android:gravity="center"
                            android:paddingTop="@dimen/pd_sm"
                            android:paddingBottom="@dimen/pd_sm"
                            android:text="@string/text_selected_floor"
                            android:textColor="@android:color/white"
                            android:textSize="@dimen/ts_common"
                            app:drawableStartCompat="@drawable/ic_floors" />

                        <TextView
                            android:id="@+id/tv_selected_floors"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="start"
                            android:paddingStart="50dp"
                            android:paddingTop="@dimen/pd_sm"
                            android:paddingBottom="@dimen/pd_sm"
                            android:textColor="@android:color/white"
                            android:textSize="@dimen/ts_common" />
                    </LinearLayout>

                </ScrollView>

                <Button
                    android:id="@+id/btn_start_disinfection"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal|bottom"
                    android:layout_marginTop="@dimen/mr_md"
                    android:layout_marginBottom="@dimen/mr_lg"
                    android:background="@drawable/bg_btn_start_task"
                    android:paddingLeft="@dimen/pd_lg"
                    android:paddingTop="@dimen/pd_md"
                    android:paddingRight="@dimen/pd_lg"
                    android:paddingBottom="@dimen/pd_md"
                    android:text="@string/text_start_disinfection"
                    android:textAllCaps="false"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_xl" />

            </LinearLayout>
        </LinearLayout>
    </LinearLayout>

    <!--侧滑菜单-->
    <LinearLayout
        android:id="@+id/ll_drawer"
        android:layout_width="400dp"
        android:layout_height="match_parent"
        android:layout_gravity="end"
        android:background="#efefef"
        android:clickable="true"
        android:focusable="true"
        android:gravity="start"
        android:orientation="vertical"
        android:paddingLeft="40dp"
        android:paddingRight="40dp">

        <Button
            android:id="@+id/btn_reloc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/mr_sm"
            android:layout_marginBottom="20dp"
            android:background="@drawable/selector_common_button"
            android:gravity="center"
            android:paddingLeft="@dimen/pd_md"
            android:paddingTop="@dimen/pd_lg"
            android:paddingRight="@dimen/pd_md"
            android:paddingBottom="@dimen/pd_lg"
            android:text="@string/text_reloc"
            android:textAllCaps="false"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_common" />

        <Button
            android:id="@+id/btn_choose_map"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/mr_sm"
            android:layout_marginBottom="20dp"
            android:background="@drawable/selector_common_button"
            android:gravity="center"
            android:paddingLeft="@dimen/pd_md"
            android:paddingTop="@dimen/pd_lg"
            android:paddingRight="@dimen/pd_md"
            android:paddingBottom="@dimen/pd_lg"
            android:text="@string/text_choose_map"
            android:textAllCaps="false"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_common" />

        <Button
            android:id="@+id/btn_choose_wifi"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/mr_sm"
            android:layout_marginBottom="20dp"
            android:background="@drawable/selector_common_button"
            android:gravity="center"
            android:paddingLeft="@dimen/pd_md"
            android:paddingTop="@dimen/pd_lg"
            android:paddingRight="@dimen/pd_md"
            android:paddingBottom="@dimen/pd_lg"
            android:text="@string/text_choose_wifi"
            android:textAllCaps="false"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_common" />

        <Button
            android:id="@+id/btn_build_map"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/mr_sm"
            android:layout_marginBottom="20dp"
            android:background="@drawable/selector_common_button"
            android:gravity="center"
            android:paddingLeft="@dimen/pd_md"
            android:paddingTop="@dimen/pd_lg"
            android:paddingRight="@dimen/pd_md"
            android:paddingBottom="@dimen/pd_lg"
            android:text="@string/text_build_map"
            android:textAllCaps="false"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_common"
            android:visibility="gone"/>

        <Button
            android:id="@+id/btn_elevator_settings"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/mr_sm"
            android:layout_marginBottom="20dp"
            android:background="@drawable/selector_common_button"
            android:gravity="center"
            android:paddingLeft="@dimen/pd_md"
            android:paddingTop="@dimen/pd_lg"
            android:paddingRight="@dimen/pd_md"
            android:paddingBottom="@dimen/pd_lg"
            android:text="@string/text_elevator_setting"
            android:textAllCaps="false"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_common" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/text_current_ip"
                android:textColor="@color/color_document_grey"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_navigation_ip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/mr_xs"
                android:ellipsize="end"
                android:singleLine="true"
                android:textColor="@color/color_document_grey"
                android:textSize="@dimen/ts_md" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/text_current_wifi"
                android:textColor="@color/color_document_grey"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_navigation_wifi"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/mr_xs"
                android:ellipsize="end"
                android:singleLine="true"
                android:textColor="@color/color_document_grey"
                android:textSize="@dimen/ts_md" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/text_navigation_version"
                android:textColor="@color/color_document_grey"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_navigation_version"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/mr_xs"
                android:ellipsize="end"
                android:singleLine="true"
                android:textColor="@color/color_document_grey"
                android:textSize="@dimen/ts_md" />
        </LinearLayout>


        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/text_current_map"
                android:textColor="@color/color_document_grey"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_current_map"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/mr_xs"
                android:ellipsize="end"
                android:singleLine="true"
                android:textColor="@color/color_document_grey"
                android:textSize="@dimen/ts_md" />
        </LinearLayout>
    </LinearLayout>
</androidx.drawerlayout.widget.DrawerLayout>