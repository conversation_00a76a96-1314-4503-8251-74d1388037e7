<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/img_guide_line"
    tools:context=".activities.GuideActivity">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tv_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableStart="@drawable/ic_back"
            android:padding="@dimen/pd_lg"
            android:text="@string/text_back"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_xl" />


    </RelativeLayout>


    <TextView
        android:id="@+id/tv_select_robot_type"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="65dp"
        android:layout_marginTop="200dp"
        android:drawableTop="@drawable/img_choose_robot"
        android:gravity="center"
        android:padding="@dimen/pd_xl"
        android:text="@string/voice_select_robot_type"
        android:textColor="@android:color/white"
        android:textSize="@dimen/ts_xl" />

    <TextView
        android:id="@+id/tv_connect_wifi"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="255dp"
        android:layout_marginTop="140dp"
        android:drawableTop="@drawable/img_connect_wifi"
        android:gravity="center"
        android:padding="@dimen/pd_xl"
        android:text="@string/voice_please_connect_wifi"
        android:textColor="@android:color/white"
        android:textSize="@dimen/ts_xl" />


    <TextView
        android:id="@+id/tv_login"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="470dp"
        android:layout_marginTop="220dp"
        android:drawableTop="@drawable/img_login"
        android:gravity="center"
        android:padding="@dimen/pd_xl"
        android:text="@string/voice_please_login"
        android:textColor="@android:color/white"
        android:textSize="@dimen/ts_xl" />


    <TextView
        android:id="@+id/tv_enter_app"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="700dp"
        android:layout_marginTop="220dp"
        android:drawableTop="@drawable/img_enter_app"
        android:gravity="center"
        android:padding="@dimen/pd_xl"
        android:text="@string/text_enter_app"
        android:textColor="@android:color/white"
        android:textSize="@dimen/ts_xl" />


    <TextView
        android:id="@+id/tv_exit_app"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_alignParentBottom="true"
        android:drawableStart="@drawable/ic_exit_app"
        android:drawablePadding="@dimen/pd_xs"
        android:gravity="center"
        android:padding="@dimen/pd_xl"
        android:text="@string/text_exit_app"
        android:textColor="#cdcdcd"
        android:textSize="@dimen/ts_common" />

</RelativeLayout>