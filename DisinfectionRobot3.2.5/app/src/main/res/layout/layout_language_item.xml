<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="@dimen/pd_lg">

    <ImageView
        android:id="@+id/iv_national_flag"
        android:layout_width="50dp"
        android:layout_height="32dp"
        android:scaleType="center"
        android:layout_alignParentStart="true"
        android:layout_centerVertical="true" />

    <TextView
        android:id="@+id/tv_language_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="20dp"
        android:layout_toEndOf="@id/iv_national_flag"
        android:textColor="@android:color/white"
        android:textSize="@dimen/ts_common" />

    <ImageView
        android:id="@+id/iv_language_state"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:src="@drawable/icon_language_normal" />
</RelativeLayout>