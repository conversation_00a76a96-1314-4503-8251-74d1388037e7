import java.text.SimpleDateFormat

plugins {
    id 'com.android.application'
}

android {
    compileSdkVersion 30
    buildToolsVersion "30.0.3"

    defaultConfig {
        applicationId "com.reeman.robot.disinfection"
        minSdkVersion 19
        targetSdkVersion 30
        versionCode 325
        versionName "v3.2.5_elevator"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        buildConfigField 'String', 'TAG', '"xuedong"'
        buildConfigField 'String', 'BUGLY', '"fec460176e"'

        ndk {
            abiFilters 'armeabi', 'armeabi-v7a'
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    sourceSets {
        main {
            jniLibs.srcDirs = ['jniLibs']
        }
    }
    android.applicationVariants.all {
        variant ->
            variant.outputs.all {
                outputFileName = "disinfection_kagao_${defaultConfig.versionName}-${getCurrentTime()}.apk"
            }
    }

}

def static getCurrentTime() {
    SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmm");
    Date curDate = new Date(System.currentTimeMillis());
    return formatter.format(curDate);
}


dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar','*.aar'])
    implementation 'androidx.appcompat:appcompat:1.2.0'
    implementation 'com.google.android.material:material:1.2.1'
    implementation 'androidx.constraintlayout:constraintlayout:2.0.1'
    //日志
    implementation 'com.elvishew:xlog:1.10.1'
    //播放gif
    implementation 'pl.droidsonroids.gif:android-gif-drawable:1.2.22'
    implementation 'com.contrarywind:Android-PickerView:4.1.9'
    //bug上报
    implementation 'com.tencent.bugly:nativecrashreport:latest.release'
    implementation 'com.tencent.bugly:crashreport_upgrade:latest.release'
    //json解析
    implementation 'com.google.code.gson:gson:2.8.6'
    //异步框架
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.squareup.retrofit2:adapter-rxjava3:2.9.0'
    implementation 'io.reactivex.rxjava3:rxjava:3.0.12'
    implementation 'io.reactivex.rxjava3:rxandroid:3.0.0'
    //数据库
    implementation "androidx.room:room-rxjava3:2.3.0"
    implementation "androidx.room:room-runtime:2.3.0"
    annotationProcessor "androidx.room:room-compiler:2.3.0"
    //下拉刷新
    implementation "androidx.swiperefreshlayout:swiperefreshlayout:1.0.0"
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'com.kyleduo.switchbutton:library:2.1.0'
    implementation 'com.github.ybq:Android-SpinKit:1.4.0'
    //eventbus
    implementation "org.greenrobot:eventbus:3.3.1"

    implementation 'com.google.zxing:core:3.3.0'
    implementation 'io.jsonwebtoken:jjwt-api:0.11.2'
    runtimeOnly 'io.jsonwebtoken:jjwt-impl:0.11.2'
    runtimeOnly('io.jsonwebtoken:jjwt-orgjson:0.11.2') {
        exclude group: 'org.json', module: 'json' //provided by Android natively
    }
    implementation 'com.microsoft.cognitiveservices.speech:client-sdk:1.19.0'

    implementation 'com.github.Liberuman:ShadowDrawable:0.1'
    testImplementation 'junit:junit:4.+'
    androidTestImplementation 'androidx.test.ext:junit:1.1.2'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.3.0'
    implementation 'com.aill:AndroidSerialPort:1.0.8'
    implementation 'com.gongwen:marqueelibrary:1.1.3'
}