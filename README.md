# REEMAN Circular Chassis Navigation Robot Documentation

This repository contains comprehensive documentation for the REEMAN Circular Chassis Navigation Robot with disinfection capabilities, running on Raspberry Pi platform.

## Documentation Structure

- [Technical Summary](docs/technical-summary.md) - Executive overview and key specifications
- [Robot Overview](docs/robot-overview.md) - General specifications and capabilities
- [Hardware Platform](docs/hardware-platform.md) - Hardware details including Raspberry Pi integration
- [Software Architecture](docs/software-architecture.md) - Software components and system design
- [SDK Documentation](docs/sdk-documentation.md) - REEMAN SDK 3.0 integration guide
- [API Reference](docs/api-reference.md) - Complete API documentation
- [Setup and Configuration](docs/setup-configuration.md) - Installation and configuration procedures
- [Application Features](docs/application-features.md) - Disinfection robot application capabilities

## Quick Start

1. Start with the [Technical Summary](docs/technical-summary.md) for executive overview
2. Review the [Robot Overview](docs/robot-overview.md) for basic understanding
3. Follow the [Setup and Configuration](docs/setup-configuration.md) guide for initial setup
4. Refer to [SDK Documentation](docs/sdk-documentation.md) for development integration
5. Use [API Reference](docs/api-reference.md) for programming interfaces

## Key Features

- **Autonomous Navigation**: SLAM-based navigation with 270° LiDAR
- **Disinfection Capabilities**: UV disinfection with automated task scheduling
- **Raspberry Pi Platform**: Linux-based navigation system with Android control interface
- **Elevator Integration**: Multi-floor operation with elevator control
- **Web API**: RESTful API for remote control and monitoring
- **SDK Support**: Android SDK for custom application development

## Support

For technical support and additional information, refer to the individual documentation files in the `docs/` directory.
