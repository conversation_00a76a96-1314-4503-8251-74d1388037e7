# REEMAN Robot Platform Documentation

This repository contains comprehensive documentation for the REEMAN robot platform, including the Moon Knight 2.0 heavy-duty chassis and Circular Chassis variants. The platform supports diverse applications from autonomous disinfection to material handling and logistics operations, all running on Raspberry Pi-based navigation systems.

## Documentation Structure

- [Technical Summary](docs/technical-summary.md) - Executive overview and key specifications
- [Robot Overview](docs/robot-overview.md) - General specifications and capabilities
- [Hardware Platform](docs/hardware-platform.md) - Hardware details including Raspberry Pi integration
- [Software Architecture](docs/software-architecture.md) - Software components and system design
- [SDK Documentation](docs/sdk-documentation.md) - REEMAN SDK 3.0 integration guide
- [API Reference](docs/api-reference.md) - Complete API documentation
- [Setup and Configuration](docs/setup-configuration.md) - Installation and configuration procedures
- [Application Features](docs/application-features.md) - Disinfection robot application capabilities

## Quick Start

1. Start with the [Technical Summary](docs/technical-summary.md) for executive overview
2. Review the [Robot Overview](docs/robot-overview.md) for basic understanding
3. Follow the [Setup and Configuration](docs/setup-configuration.md) guide for initial setup
4. Refer to [SDK Documentation](docs/sdk-documentation.md) for development integration
5. Use [API Reference](docs/api-reference.md) for programming interfaces

## Key Features

### Moon Knight 2.0 Heavy-Duty Platform
- **60kg Payload Capacity**: Industrial-grade material handling
- **SLAM 2.0 Navigation**: ±10mm precision without QR codes required
- **Extended Operation**: 15-20 hours with 30kg load
- **Fleet Coordination**: Multi-robot collaboration and traffic management
- **Dual 3D Cameras**: Advanced environment perception and obstacle avoidance

### Circular Chassis Disinfection Platform
- **UV Disinfection**: Specialized disinfection equipment integration
- **QR-Assisted Precision**: Enhanced positioning for exact coverage patterns
- **Healthcare Optimized**: Designed for hospitals, offices, and public spaces
- **Safety Systems**: Human detection and automatic shutdown protocols

### Universal Platform Features
- **Raspberry Pi Architecture**: Linux-based navigation with Android control interface
- **Elevator Integration**: Multi-floor operation with elevator control
- **Web API**: RESTful API for remote control and monitoring
- **SDK Support**: Unified development tools across platform variants
- **270° LiDAR**: Advanced SLAM navigation with real-time mapping

## Support

For technical support and additional information, refer to the individual documentation files in the `docs/` directory.
