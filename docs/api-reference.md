# API Reference

## Overview

The REEMAN robot provides comprehensive REST API access for remote control, monitoring, and integration. All APIs use HTTP/HTTPS protocols with JSON data format.

## Base Configuration

### Connection Setup
```javascript
// Replace with your robot's IP address
const ROBOT_IP = "*************";
const BASE_URL = `http://${ROBOT_IP}`;

// Note: IP cannot be in 10.x.x.x network segment
// 127.0.0.1 indicates robot is not connected to network
```

### Authentication
Currently, the API does not require authentication, but ensure your client is on the same LAN as the robot.

## System Information APIs

### Get Navigation Version
```http
GET /reeman/current_version
```

**Response:**
```json
{
  "version": "v1.1.0"
}
```

### Get Robot Hostname
```http
GET /reeman/hostname
```

**Response:**
```json
{
  "hostname": "reeman-robot"
}
```

### Get Robot Position
```http
GET /reeman/pose
```

**Response:**
```json
{
  "x": 297,
  "y": 251,
  "theta": 0.97
}
```

### Get System Status
```http
GET /reeman/base_encode
```

**Response:**
```json
{
  "battery": 85,
  "chargeFlag": 1,
  "emergencyButton": 1
}
```

**Status Codes:**
- `chargeFlag`: 2=charging station, 3=adapter charging, 8=connecting to pile, other=not charging
- `emergencyButton`: 0=pressed, 1=released
- `battery`: 0-100 percentage

### Get Current Speed
```http
GET /reeman/speed
```

**Response:**
```json
{
  "vx": 0.3,
  "vth": 0.5
}
```

## Navigation APIs

### Navigate to Coordinates
```http
POST /cmd/nav
Content-Type: application/json

{
  "x": 285,
  "y": 252,
  "theta": 1.6
}
```

**Response:**
```json
{
  "status": "success"
}
```

### Navigate to Named Point
```http
POST /cmd/nav_name
Content-Type: application/json

{
  "point": "A"
}
```

**Response:**
```json
{
  "status": "success"
}
```

### Get Navigation Status
```http
GET /reeman/nav_status
```

**Response:**
```json
{
  "res": 3,
  "reason": 0,
  "goal": "A",
  "dist": 1.8,
  "mileage": 2.3
}
```

**Navigation States:**
- `res=1`: Navigation started
- `res=3`: Navigation completed (reason=0: success, reason=1: failed)
- `res=4`: Navigation cancelled manually
- `res=6`: Normal operation

**Error Codes (when res=6):**
- `reason=0`: Success
- `reason=1`: Connecting to charging pile
- `reason=2`: Emergency stop pressed
- `reason=3`: Adapter charging
- `reason=4`: Target point not found
- `reason=5`: AGV docking failed
- `reason=6`: Positioning abnormality
- `reason=7`: Fixed route points too far apart
- `reason=8`: Fixed route not found
- `reason=9`: Failed to read point information

### Cancel Navigation
```http
POST /cmd/cancel_goal
```

**Response:**
```json
{
  "status": "success"
}
```

### Get Navigation Path
```http
GET /reeman/global_plan
```

**Response:**
```json
{
  "coordinates": [
    [0.826, -0.35],
    [0.86, -0.38],
    [0.89, -0.41]
  ]
}
```

### Set Maximum Speed
```http
POST /cmd/max_speed
Content-Type: application/json

{
  "speed": 0.5
}
```

**Speed Range:** 0.3 to 1.0 m/s

## Charging APIs

### Navigate to Charging Station
```http
POST /cmd/charge
Content-Type: application/json

{
  "type": 2,
  "point": "charging_pile"
}
```

**Charge Types:**
- `type=0`: Direct docking with charging pile
- `type=1`: Cancel docking with charging pile
- `type=2`: Navigate to charging pile and start docking

## Map Management APIs

### Get Current Map
```http
GET /reeman/map
```

**Response:** Returns complete map data including size and coordinate information.

### Get Current Map Name
```http
GET /reeman/current_map
```

**Response:**
```json
{
  "alias": "Office Floor 1",
  "name": "674e32391fad7ad8a1422360357d0516"
}
```

### Get All Maps
```http
GET /reeman/history_map
```

**Response:** Returns array of all available maps.

### Switch to Mapping Mode
```http
POST /cmd/set_mode
Content-Type: application/json

{
  "mode": 1
}
```

**Modes:**
- `mode=1`: Mapping mode
- `mode=2`: Navigation mode
- `mode=3`: Incremental mapping mode

### Save Map
```http
POST /cmd/save_map
```

### Apply Map
```http
POST /cmd/apply_map
Content-Type: application/json

{
  "name": "map_name_or_id"
}
```

### Export Map
```http
POST /download/export_map
Content-Type: application/json

{
  "name": "map_name"
}
```

**Response:** Binary map file download

### Upload Map
```http
POST /upload/import_map
Content-Type: multipart/form-data

FormData with 'file' field containing map file
```

### Rename Map
```http
POST /cmd/rename_map
Content-Type: application/json

{
  "name": "5d81b1a*****e6a48",
  "alias": "New Map Name"
}
```

### Delete Map
```http
POST /cmd/delete_map
Content-Type: application/json

{
  "name": "map_name"
}
```

## Position Management APIs

### Set Calibration Position
```http
POST /cmd/position
Content-Type: application/json

{
  "name": "point_name",
  "type": "delivery",
  "pose": {
    "x": -3.05,
    "y": 0.41,
    "theta": 0.56
  }
}
```

**Point Types:**
- `delivery`: Distribution point
- `normal`: Route point
- `production`: Production point
- `charge`: Charging pile

### Get Calibration Positions
```http
GET /reeman/position
```

**Response:**
```json
{
  "waypoints": [
    {
      "name": "1",
      "type": "delivery",
      "pose": {
        "x": 2.06,
        "y": 0.77,
        "theta": 0.19
      }
    }
  ]
}
```

### Delete Calibration Position
```http
DELETE /cmd/position
Content-Type: application/json

{
  "name": "point_name"
}
```

## Motion Control APIs

### Direct Speed Control
```http
POST /cmd/speed
Content-Type: application/json

{
  "vx": 0.4,
  "vth": 0.0
}
```

**Parameters:**
- `vx`: Linear velocity (m/s) - positive=forward, negative=backward
- `vth`: Angular velocity (rad/s) - positive=left turn, negative=right turn

**Examples:**
```javascript
// Move forward
{ "vx": 0.4, "vth": 0 }

// Move backward
{ "vx": -0.4, "vth": 0 }

// Turn left
{ "vx": 0, "vth": 0.5 }

// Turn right
{ "vx": 0, "vth": -0.5 }

// Stop
{ "vx": 0, "vth": 0 }
```

### Move Specific Distance
```http
POST /cmd/move
Content-Type: application/json

{
  "distance": 100,
  "direction": 1,
  "speed": 0.8
}
```

**Parameters:**
- `distance`: Distance in centimeters
- `direction`: 0=backward, 1=forward
- `speed`: Speed in m/s

### Turn Specific Angle
```http
POST /cmd/turn
Content-Type: application/json

{
  "direction": 1,
  "angle": 90,
  "speed": 0.6
}
```

**Parameters:**
- `direction`: 0=right turn, 1=left turn
- `angle`: Angle in degrees
- `speed`: Angular speed in rad/s

## Sensor Data APIs

### Get LiDAR Data
```http
GET /reeman/laser
```

**Response:**
```json
{
  "coordinates": [
    [364, 166],
    [365, 167],
    [321, 180]
  ]
}
```

### Get IMU Data
```http
GET /reeman/imu
```

**Response:**
```json
{
  "a": 0.0,
  "g": 0.0
}
```

## External Control APIs

### Power Control
```http
POST /cmd/lock      # Turn on external power
POST /cmd/unlock    # Turn off external power
```

### Hydraulic Control (if equipped)
```http
POST /cmd/hydraulic_up    # Raise hydraulic system
POST /cmd/hydraulic_down  # Lower hydraulic system
```

## Virtual Wall APIs

### Get Virtual Walls
```http
GET /reeman/restrict_layer
```

**Response:** Returns 2D array of virtual wall points.

### Update Virtual Walls
```http
POST /cmd/restrict_layer
Content-Type: application/json

{
  "waypoints": [
    {
      "pose": {
        "point1": {"x": 1.1, "y": 2.2},
        "point2": {"x": 3.3, "y": 4.4}
      }
    }
  ]
}
```

## Special Areas APIs

### Get Special Areas
```http
GET /reeman/special_polygon
```

**Response:**
```json
{
  "polygons": [
    {
      "name": "slow_zone",
      "polygon": [[x1,y1], [x2,y2], [x3,y3]],
      "speed": 0.6,
      "type": 0
    }
  ]
}
```

## System Upgrade APIs

### Upload Firmware
```http
POST /upload/upgrade
Content-Type: multipart/form-data

FormData with 'file' field containing upgrade file
```

## Error Handling

### Standard Error Response
```json
{
  "status": "error",
  "message": "Error description",
  "code": "ERROR_CODE"
}
```

### Common HTTP Status Codes
- `200`: Success
- `400`: Bad Request - Invalid parameters
- `404`: Not Found - Endpoint or resource not found
- `500`: Internal Server Error - System error

## Rate Limiting

- **Motion Commands**: Send at ~300ms intervals for continuous movement
- **Status Queries**: Recommended maximum 1Hz for monitoring
- **Map Operations**: No specific limits, but allow time for processing

## WebSocket Support

For real-time data streaming, consider implementing WebSocket connections for:
- Continuous position updates
- Real-time sensor data
- Navigation status monitoring
- System alerts and notifications
