# Software Architecture

## System Overview

The REEMAN robot employs a layered software architecture with clear separation between navigation, control, and user interface components. The system runs on a dual-platform setup with Linux-based ROS navigation and Android-based user interface.

## Architecture Layers

```
┌─────────────────────────────────────────────────────────┐
│                 User Interface Layer                    │
│  ┌─────────────────┐  ┌─────────────────┐              │
│  │  Android App    │  │   Web Interface │              │
│  │   (Touch UI)    │  │  (Browser-based)│              │
│  └─────────────────┘  └─────────────────┘              │
└─────────────────────────────────────────────────────────┘
                           │
┌─────────────────────────────────────────────────────────┐
│                Application Layer                        │
│  ┌─────────────────┐  ┌─────────────────┐              │
│  │ Task Management │  │  API Services   │              │
│  │   & Scheduling  │  │   (REST API)    │              │
│  └─────────────────┘  └─────────────────┘              │
└─────────────────────────────────────────────────────────┘
                           │
┌─────────────────────────────────────────────────────────┐
│                Control Layer                            │
│  ┌─────────────────┐  ┌─────────────────┐              │
│  │ Robot Controller│  │ Navigation Mgr  │              │
│  │   (Commands)    │  │   (Path Plan)   │              │
│  └─────────────────┘  └─────────────────┘              │
└─────────────────────────────────────────────────────────┘
                           │
┌─────────────────────────────────────────────────────────┐
│                Hardware Layer                           │
│  ┌─────────────────┐  ┌─────────────────┐              │
│  │   ROS System    │  │  Sensor Drivers │              │
│  │   (Linux)       │  │   (Hardware)    │              │
│  └─────────────────┘  └─────────────────┘              │
└─────────────────────────────────────────────────────────┘
```

## Core Components

### 1. Android Application Layer

#### Main Application (DisinfectionRobot v3.2.5)
- **Package**: `com.reeman.robot.disinfection`
- **Architecture**: MVP (Model-View-Presenter) pattern
- **Language**: Java with Android SDK API level 30
- **Features**: Task management, navigation control, system settings

#### Key Activities
```java
// Main application activities
MainActivity.java           // Primary control interface
TaskCreateActivity.java     // Task creation and scheduling
TaskExecutingActivity.java  // Real-time task monitoring
MapBuildingActivity.java    // Map creation interface
SettingActivity.java        // System configuration
ElevatorSettingActivity.java // Elevator integration
WiFiConnectActivity.java    // Network configuration
```

#### Core Controllers
```java
// Primary control classes
RobotActionController.java  // Main robot command interface
NavigationManager.java     // Navigation coordination
ElevatorController.java    // Elevator integration
AccessController.java      // Security and access control
```

### 2. ROS Navigation System

#### Navigation Stack
- **SLAM Algorithm**: Real-time mapping and localization
- **Path Planning**: Dynamic route calculation
- **Obstacle Avoidance**: Multi-sensor fusion
- **Localization**: Particle filter-based positioning

#### Core Services
```bash
# ROS navigation services
/reeman/pose              # Robot position service
/reeman/nav_status        # Navigation status
/reeman/map               # Map data service
/reeman/laser             # LiDAR data
/cmd/nav                  # Navigation commands
/cmd/speed                # Motion control
```

### 3. Communication Layer

#### Serial Communication
```java
// Serial port configuration
public class RobotActionController {
    private static final String ROS_SERIAL_PORT = "/dev/ttyS1";
    private static final int BAUD_RATE = 115200;
    
    // Command protocol examples
    public void startDisinfection() {
        robotActionProvider.sendRosCom("sys:lock");
    }
    
    public void navigationByPoint(String point) {
        robotActionProvider.sendRosCom("point[" + point + "]");
    }
}
```

#### Network Communication
- **Web API**: RESTful services on port 80
- **WebSocket**: Real-time data streaming
- **WiFi Management**: Network configuration and monitoring
- **Remote Access**: Cloud connectivity for monitoring

### 4. Data Management

#### Database Layer
```java
// Room database implementation
@Database(entities = {Task.class}, version = 1)
public abstract class AppDatabase extends RoomDatabase {
    public abstract TaskDao taskDao();
}

// Task entity for scheduling
@Entity
public class Task {
    public String taskName;
    public String startTime;
    public int repeatTime;
    public boolean enabled;
    public List<String> points;
}
```

#### Shared Preferences
```java
// Configuration management
public class SpManager {
    public static final String NAV_SPEED = "nav_speed";
    public static final String LOW_POWER = "low_power";
    public static final String VOICE_BROADCAST = "voice_broadcast";
    public static final String LOCK_SCREEN = "lock_screen";
}
```

## API Architecture

### REST API Endpoints

#### Navigation Control
```http
GET  /reeman/pose                    # Get robot position
POST /cmd/nav                        # Navigate to coordinates
POST /cmd/nav_name                   # Navigate to named point
GET  /reeman/nav_status              # Get navigation status
POST /cmd/cancel_goal                # Cancel navigation
```

#### Map Management
```http
GET  /reeman/map                     # Get current map
POST /cmd/set_mode                   # Switch mapping/navigation mode
POST /cmd/save_map                   # Save current map
POST /cmd/apply_map                  # Apply specific map
GET  /reeman/history_map             # List all maps
```

#### System Control
```http
GET  /reeman/base_encode             # Battery and system status
POST /cmd/speed                      # Direct motion control
POST /cmd/lock                       # Enable external power
POST /cmd/unlock                     # Disable external power
```

### Event-Driven Architecture

#### EventBus Implementation
```java
// Event system for component communication
public class Event {
    public static Event getNavResEvent(String result) {
        return new Event(MsgType.NAV_RES, result);
    }
    
    public static Event getPositionEvent(String position) {
        return new Event(MsgType.POSITION, position);
    }
    
    public static Event getBatteryEvent(Intent intent) {
        return new Event(MsgType.BATTERY, intent);
    }
}
```

## Task Management System

### Scheduling Engine
```java
// Automated task scheduling
private void onTimeStamp() {
    // Check for scheduled tasks
    dbRepository.getCurrentScheduleTask()
        .subscribeOn(Schedulers.io())
        .observeOn(Schedulers.io())
        .subscribeWith(new MaybeObserver<List<Task>>() {
            @Override
            public void onSuccess(List<Task> tasks) {
                for (Task task : tasks) {
                    if (isTaskReady(task)) {
                        executeTask(task);
                    }
                }
            }
        });
}
```

### Task Execution Flow
```
Task Creation → Validation → Scheduling → Execution → Monitoring → Completion
     │              │           │            │           │            │
     ▼              ▼           ▼            ▼           ▼            ▼
  User Input → Point Check → Timer Set → Navigation → Status Track → Report
```

## Safety and Error Handling

### Emergency Systems
```java
// Emergency stop handling
public class RobotReceiver extends BroadcastReceiver {
    @Override
    public void onReceive(Context context, Intent intent) {
        switch (intent.getAction()) {
            case "REEMAN_BROADCAST_SCRAMSTATE":
                EventBus.getDefault().post(Event.getEmergencyStopEvent(intent));
                break;
            case "AUTOCHARGE_ERROR_DOCKNOTFOUND":
                EventBus.getDefault().post(Event.getDockFailedEvent());
                break;
        }
    }
}
```

### Error Recovery
- **Connection Loss**: Automatic reconnection protocols
- **Navigation Failure**: Alternative path calculation
- **Hardware Errors**: Graceful degradation and alerts
- **Power Management**: Automatic charging and low-power handling

## Integration Interfaces

### SDK Integration
```java
// SDK initialization example
RobotActionController.getInstance().init(
    115200,                    // Baud rate
    "/dev/ttyS1",             // Serial port
    new RosCallbackParser.RosCallback() {
        @Override
        public void onResult(String result) {
            // Handle ROS responses
        }
    },
    BuildConfig.APP_LOG_DIR   // Log directory
);
```

### Third-Party Integration
- **Elevator Systems**: Serial protocol for elevator control
- **Access Control**: Door and gate integration
- **Building Management**: HVAC and lighting coordination
- **Monitoring Systems**: Real-time status reporting

## Performance Optimization

### Memory Management
- **Efficient Data Structures**: Optimized for real-time processing
- **Garbage Collection**: Minimized allocations in critical paths
- **Resource Pooling**: Reusable objects for frequent operations

### Real-Time Processing
- **Thread Management**: Dedicated threads for critical operations
- **Priority Scheduling**: High-priority navigation and safety tasks
- **Latency Optimization**: Sub-100ms response times for safety systems

## Logging and Diagnostics

### Comprehensive Logging
```java
// Multi-level logging system
XLog.init();
Timber.plant(new FileLoggingTree(
    Log.VERBOSE,                    // Log level
    BuildConfig.DEBUG,              // Console output
    Environment.getExternalStorageDirectory().getPath(),
    BuildConfig.APP_LOG_DIR,        // Log directory
    Arrays.asList(                  // Log categories
        BuildConfig.APP_LOG_DIR,
        BuildConfig.LOG_POWER_BOARD
    )
));
```

### System Monitoring
- **Performance Metrics**: CPU, memory, and network usage
- **Health Checks**: Continuous system diagnostics
- **Remote Monitoring**: Cloud-based system oversight
- **Predictive Maintenance**: Proactive issue detection
