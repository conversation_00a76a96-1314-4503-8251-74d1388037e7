# SDK Documentation

## REEMAN SDK 3.0 Overview

The REEMAN SDK 3.0 provides comprehensive integration capabilities for developing custom applications that interface with the robot's navigation and control systems. The SDK supports both Android development and web-based integration.

## SDK Components

### 1. Android SDK Integration

#### Gradle Dependencies
```groovy
// Project-level build.gradle
pluginManagement {
    repositories {
        maven { url 'https://jitpack.io' }
    }
}

// App-level build.gradle
dependencies {
    implementation 'com.github.Misaka-XXXXII:reeman-lib:1.1.0'
    implementation 'com.github.Misaka-XXXXII.reeman-lib:serialport:1.1.0'
}
```

#### SDK Initialization
```java
// Initialize logging system
XLog.init();
Timber.plant(new FileLoggingTree(
    Log.VERBOSE,                    // Log level
    BuildConfig.DEBUG,              // Console output
    Environment.getExternalStorageDirectory().getPath(),
    BuildConfig.APP_LOG_DIR,        // Log tag
    Arrays.asList(                  // Log categories
        BuildConfig.APP_LOG_DIR,
        com.reeman.serialport.BuildConfig.LOG_POWER_BOARD
    )
));

// Initialize robot controller
RobotActionController.getInstance().init(
    115200,                         // Baud rate
    "/dev/ttyS1",                  // Serial port
    new RosCallbackParser.RosCallback() {
        @Override
        public void onResult(String result) {
            // Handle ROS system responses
            processRosResponse(result);
        }
    },
    BuildConfig.APP_LOG_DIR         // Log directory
);
```

### 2. Serial Communication Protocol

#### Command Structure
```java
// Basic command sending
public void sendCommand(String command) {
    RobotActionController.getInstance().sendRosCom(command);
}

// Navigation commands
public void navigateToPoint(String pointName) {
    sendCommand("point[" + pointName + "]");
}

// System control commands
public void enableDisinfection() {
    sendCommand("sys:lock");
}

public void disableDisinfection() {
    sendCommand("sys:unlock");
}
```

#### Response Handling
```java
// ROS callback implementation
private class RosCallback extends OnROSListener {
    @Override
    public void onResult(String result) {
        if (result.startsWith("nav_res")) {
            handleNavigationResult(result);
        } else if (result.startsWith("current_map")) {
            handleMapUpdate(result);
        } else if (result.startsWith("move_status:4")) {
            handleObstacleDetection();
        }
    }
}
```

## Web API Integration

### 1. Basic Information APIs

#### Get Robot Status
```javascript
// Get current robot position
fetch('http://*************/reeman/pose')
    .then(response => response.json())
    .then(data => {
        console.log('Position:', data.x, data.y, data.theta);
    });

// Get navigation version
fetch('http://*************/reeman/current_version')
    .then(response => response.json())
    .then(data => {
        console.log('Version:', data.version);
    });
```

#### System Status Monitoring
```javascript
// Get battery and system status
async function getSystemStatus() {
    const response = await fetch('http://*************/reeman/base_encode');
    const status = await response.json();
    
    return {
        battery: status.battery,        // 0-100%
        charging: status.chargeFlag,    // 2=charging station, 3=adapter
        emergency: status.emergencyButton // 0=pressed, 1=released
    };
}
```

### 2. Navigation Control APIs

#### Point-to-Point Navigation
```javascript
// Navigate to specific coordinates
async function navigateToCoordinates(x, y, theta) {
    const response = await fetch('http://*************/cmd/nav', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ x: x, y: y, theta: theta })
    });
    return response.json();
}

// Navigate to named point
async function navigateToPoint(pointName) {
    const response = await fetch('http://*************/cmd/nav_name', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ point: pointName })
    });
    return response.json();
}
```

#### Navigation Status Monitoring
```javascript
// Get real-time navigation status
async function getNavigationStatus() {
    const response = await fetch('http://*************/reeman/nav_status');
    const status = await response.json();
    
    return {
        state: status.res,           // Navigation state
        result: status.reason,       // Result code
        target: status.goal,         // Target point
        distance: status.dist,       // Distance to goal
        mileage: status.mileage      // Distance traveled
    };
}

// Navigation state codes:
// state=1: Navigation started
// state=3: Navigation completed (reason=0: success, reason=1: failed)
// state=4: Navigation cancelled
// state=6: Normal operation
```

### 3. Map Management APIs

#### Map Operations
```javascript
// Get current map data
async function getCurrentMap() {
    const response = await fetch('http://*************/reeman/map');
    return response.json();
}

// Switch to mapping mode
async function startMapping() {
    const response = await fetch('http://*************/cmd/set_mode', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ mode: 1 })
    });
    return response.json();
}

// Save current map
async function saveMap() {
    const response = await fetch('http://*************/cmd/save_map', {
        method: 'POST'
    });
    return response.json();
}
```

#### Map Management
```javascript
// Apply specific map
async function applyMap(mapName) {
    const response = await fetch('http://*************/cmd/apply_map', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: mapName })
    });
    return response.json();
}

// Get list of all maps
async function getAllMaps() {
    const response = await fetch('http://*************/reeman/history_map');
    return response.json();
}
```

### 4. Motion Control APIs

#### Direct Motion Control
```javascript
// Move robot with velocity commands
async function moveRobot(linearVel, angularVel) {
    const response = await fetch('http://*************/cmd/speed', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
            vx: linearVel,    // m/s (positive=forward, negative=backward)
            vth: angularVel   // rad/s (positive=left, negative=right)
        })
    });
    return response.json();
}

// Move specific distance
async function moveDistance(distance, direction, speed) {
    const response = await fetch('http://*************/cmd/move', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            distance: distance,  // cm
            direction: direction, // 0=backward, 1=forward
            speed: speed         // m/s
        })
    });
    return response.json();
}
```

## Advanced Integration Examples

### 1. Complete Navigation Workflow
```java
public class NavigationWorkflow {
    private RobotActionController controller;
    
    public void executeNavigationTask(String targetPoint) {
        // 1. Check current status
        controller.getCurrentPosition();
        
        // 2. Start navigation
        controller.navigationByPoint(targetPoint);
        
        // 3. Monitor progress
        monitorNavigation(targetPoint);
    }
    
    private void monitorNavigation(String target) {
        // Monitor navigation status through callbacks
        EventBus.getDefault().register(new Object() {
            @Subscribe
            public void onNavigationResult(Event event) {
                if (event.type == MsgType.NAV_RES) {
                    handleNavigationComplete(event.data);
                }
            }
        });
    }
}
```

### 2. Automated Task Scheduling
```java
public class TaskScheduler {
    public void scheduleDisinfectionTask(List<String> points, String startTime) {
        Task task = new Task();
        task.taskName = "Automated Disinfection";
        task.startTime = startTime;
        task.points = points;
        task.enabled = true;
        
        // Save to database
        dbRepository.insertTask(task);
        
        // Schedule execution
        scheduleTaskExecution(task);
    }
    
    private void executeDisinfectionSequence(List<String> points) {
        for (String point : points) {
            // Navigate to point
            controller.navigationByPoint(point);
            
            // Wait for arrival
            waitForNavigation();
            
            // Start disinfection
            controller.startDisinfection();
            
            // Wait for completion
            Thread.sleep(disinfectionDuration);
            
            // Stop disinfection
            controller.stopDisinfection();
        }
    }
}
```

### 3. Real-Time Monitoring Dashboard
```javascript
class RobotMonitor {
    constructor(robotIP) {
        this.baseURL = `http://${robotIP}`;
        this.updateInterval = 1000; // 1 second
    }
    
    async startMonitoring() {
        setInterval(async () => {
            const status = await this.getFullStatus();
            this.updateDashboard(status);
        }, this.updateInterval);
    }
    
    async getFullStatus() {
        const [position, battery, navStatus] = await Promise.all([
            fetch(`${this.baseURL}/reeman/pose`).then(r => r.json()),
            fetch(`${this.baseURL}/reeman/base_encode`).then(r => r.json()),
            fetch(`${this.baseURL}/reeman/nav_status`).then(r => r.json())
        ]);
        
        return { position, battery, navStatus };
    }
    
    updateDashboard(status) {
        document.getElementById('position').textContent = 
            `X: ${status.position.x}, Y: ${status.position.y}`;
        document.getElementById('battery').textContent = 
            `${status.battery.battery}%`;
        document.getElementById('nav-state').textContent = 
            this.getNavigationStateText(status.navStatus.res);
    }
}
```

## Error Handling and Best Practices

### 1. Connection Management
```java
public class ConnectionManager {
    private static final int MAX_RETRIES = 3;
    private static final int RETRY_DELAY = 1000; // ms
    
    public boolean sendCommandWithRetry(String command) {
        for (int i = 0; i < MAX_RETRIES; i++) {
            try {
                controller.sendRosCom(command);
                return true;
            } catch (Exception e) {
                if (i == MAX_RETRIES - 1) {
                    Log.e("SDK", "Failed to send command after retries", e);
                    return false;
                }
                try {
                    Thread.sleep(RETRY_DELAY);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    return false;
                }
            }
        }
        return false;
    }
}
```

### 2. Safety Considerations
```java
public class SafetyManager {
    public void emergencyStop() {
        // Immediate stop command
        controller.cancelNavigation();
        
        // Disable all external systems
        controller.stopDisinfection();
        
        // Alert operators
        sendEmergencyAlert();
    }
    
    public boolean isOperationSafe() {
        // Check emergency stop status
        if (controller.getScramState() == 0) {
            return false;
        }
        
        // Check battery level
        int batteryLevel = getBatteryLevel();
        if (batteryLevel < 10) {
            return false;
        }
        
        return true;
    }
}
```

## SDK Resources and Support

### Documentation
- **API Reference**: Complete REST API documentation
- **Code Examples**: Sample implementations and use cases
- **Integration Guides**: Step-by-step integration tutorials
- **Troubleshooting**: Common issues and solutions

### Development Tools
- **SDK Library**: Pre-compiled Android libraries
- **Test Utilities**: Unit testing and integration testing tools
- **Debugging Tools**: Log analysis and diagnostic utilities
- **Simulation Environment**: Virtual robot for development testing
