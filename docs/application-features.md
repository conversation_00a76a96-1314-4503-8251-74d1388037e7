# Application Features

## Disinfection Robot Application v3.2.5

The REEMAN Disinfection Robot application provides comprehensive control and monitoring capabilities for autonomous disinfection operations. Built on Android 5.1, the application offers an intuitive interface for task management, navigation control, and system monitoring.

## Core Application Features

### 1. Task Management System

#### Automated Task Scheduling
```java
// Task configuration structure
public class Task {
    String taskName;           // Task identifier
    String startTime;          // Execution time (HH:MM format)
    int repeatTime;            // Weekly schedule bitmask
    boolean enabled;           // Task activation status
    List<String> points;       // Navigation waypoints
    int disinfectionDuration;  // Duration per point
}
```

#### Scheduling Capabilities
- **Daily Scheduling**: Set specific times for automatic execution
- **Weekly Patterns**: Configure different schedules for each day of week
- **Recurring Tasks**: Automatic repetition based on schedule
- **Manual Override**: Immediate task execution capability
- **Task Prioritization**: Emergency tasks override scheduled operations

#### Task Execution Flow
```
Task Creation → Validation → Scheduling → Pre-execution Check → Navigation → Disinfection → Completion Report
```

### 2. Navigation Control Interface

#### Real-Time Navigation
- **Point-to-Point Navigation**: Direct navigation to named locations
- **Route Planning**: Multi-point sequential navigation
- **Obstacle Avoidance**: Dynamic path recalculation
- **Progress Monitoring**: Real-time distance and status updates

#### Navigation Modes
```java
// Navigation mode constants
public static final int MODE_MAPPING = 1;      // Map building mode
public static final int MODE_NAVIGATION = 2;   // Normal operation
public static final int MODE_INCREMENTAL = 3;  // Map updating
```

#### Speed Control
- **Variable Speed**: 0.1 - 1.0 m/s adjustable navigation speed
- **Context-Aware**: Automatic speed adjustment in special areas
- **Safety Limits**: Maximum speed restrictions in confined spaces
- **Emergency Stop**: Immediate halt capability

### 3. Map Management

#### Interactive Map Interface
- **Real-Time Visualization**: Live robot position and path display
- **Zoom and Pan**: Detailed map exploration capabilities
- **Point Calibration**: Touch-based waypoint creation
- **Route Visualization**: Planned and executed path display

#### Map Operations
```java
// Map management functions
public void applyMap(String mapName);          // Switch active map
public void saveCurrentMap();                  // Save mapping session
public void exportMap(String mapName);         // Export for backup
public void importMap(File mapFile);           // Import existing map
```

### 4. Disinfection Control

#### UV Disinfection System
```java
// Disinfection control methods
public void startDisinfection() {
    robotActionProvider.sendRosCom("sys:lock");
}

public void stopDisinfection() {
    robotActionProvider.sendRosCom("sys:unlock");
}
```

#### Disinfection Parameters
- **Duration Control**: Configurable disinfection time per location
- **Intensity Settings**: UV light intensity adjustment
- **Coverage Patterns**: Systematic area coverage algorithms
- **Safety Protocols**: Automatic shutdown on human detection

### 5. System Monitoring

#### Real-Time Status Display
```java
// System status monitoring
public class SystemStatus {
    int batteryLevel;          // 0-100% battery charge
    int chargingStatus;        // Charging state indicator
    boolean emergencyStop;     // Emergency stop status
    String currentPosition;    // Robot location coordinates
    String navigationStatus;   // Current navigation state
    String lastTaskResult;     // Previous task completion status
}
```

#### Alert System
- **Low Battery Warnings**: Automatic charging initiation
- **Navigation Alerts**: Obstacle detection and path blocking
- **System Errors**: Hardware malfunction notifications
- **Task Completion**: Success/failure status reporting

### 6. Multi-Floor Operations

#### Elevator Integration
```java
// Elevator control system
public class ElevatorController {
    String elevatorSerialPort = "/dev/ttyUSB1";  // Default port
    boolean elevatorEnabled = true;               // Feature toggle
    
    public void callElevator(int targetFloor);
    public void enterElevator();
    public void exitElevator();
    public void selectFloor(int floor);
}
```

#### Floor Management
- **Multi-Floor Mapping**: Separate maps for each floor
- **Elevator Coordination**: Automatic elevator calling and operation
- **Floor Transition**: Seamless navigation between floors
- **Safety Protocols**: Emergency procedures for elevator failures

### 7. User Interface Components

#### Main Activity Features
```java
// Primary interface components
public class MainActivity {
    // Real-time robot status display
    private void updateRobotStatus();
    
    // Task management interface
    private void showTaskList();
    
    // Emergency controls
    private void emergencyStop();
    
    // Navigation controls
    private void startNavigation(String target);
}
```

#### Activity Structure
- **SplashActivity**: Application startup and initialization
- **MainActivity**: Primary control dashboard
- **TaskCreateActivity**: Task configuration interface
- **TaskExecutingActivity**: Real-time task monitoring
- **MapBuildingActivity**: Map creation and editing
- **SettingActivity**: System configuration
- **WiFiConnectActivity**: Network setup

### 8. Configuration Management

#### System Settings
```java
// Configuration constants
public class Constant {
    String NAV_SPEED = "0.4";              // Default navigation speed
    int LOW_POWER = 20;                    // Low battery threshold
    boolean VOICE_BROADCAST = true;        // Audio notifications
    boolean LOCK_SCREEN = false;           // Screen security
    int DELAY_TIME = 10;                   // Task delay seconds
}
```

#### User Preferences
- **Language Selection**: Chinese/English interface
- **Voice Settings**: Audio feedback configuration
- **Security Options**: Screen lock and password protection
- **Performance Tuning**: Speed and behavior adjustments

### 9. Safety Features

#### Emergency Systems
```java
// Safety monitoring and control
public class SafetyManager {
    public void monitorEmergencyStop();      // Hardware button monitoring
    public void handleObstacleDetection();   // Sensor-based stopping
    public void manageLowBattery();          // Power management
    public void enforceVirtualWalls();       // Boundary compliance
}
```

#### Safety Protocols
- **Emergency Stop**: Immediate halt of all operations
- **Obstacle Avoidance**: Multi-sensor collision prevention
- **Virtual Boundaries**: Software-defined restricted areas
- **Human Detection**: Automatic operation suspension

### 10. Data Management

#### Database Integration
```java
// Room database for task storage
@Database(entities = {Task.class}, version = 1)
public abstract class AppDatabase extends RoomDatabase {
    public abstract TaskDao taskDao();
}

// Task data access object
@Dao
public interface TaskDao {
    @Query("SELECT * FROM task WHERE enabled = 1")
    List<Task> getActiveTasks();
    
    @Insert
    void insertTask(Task task);
    
    @Update
    void updateTask(Task task);
    
    @Delete
    void deleteTask(Task task);
}
```

#### Data Persistence
- **Task Storage**: SQLite database for task management
- **Configuration Backup**: Shared preferences for settings
- **Map Data**: Persistent storage of navigation maps
- **Log Management**: Comprehensive operation logging

### 11. Communication Interfaces

#### Serial Communication
```java
// ROS communication protocol
public class RobotActionController {
    private static final String SERIAL_PORT = "/dev/ttyS1";
    private static final int BAUD_RATE = 115200;
    
    // Command examples
    public void getCurrentPosition() {
        sendRosCom("nav:get_pose");
    }
    
    public void markPoint(String[] coordinates, String pointName) {
        sendRosCom("nav:set_flag_point[" + 
                  coordinates[0] + "," + coordinates[1] + "," + 
                  coordinates[2] + "," + pointName + "]");
    }
}
```

#### Network Integration
- **WiFi Management**: Automatic network configuration
- **Web API Access**: RESTful service integration
- **Remote Monitoring**: Cloud-based status reporting
- **OTA Updates**: Over-the-air software updates

### 12. Advanced Features

#### Intelligent Scheduling
```java
// Automated task execution
private void onTimeStamp() {
    // Check for scheduled tasks
    Calendar current = Calendar.getInstance();
    for (Task task : scheduledTasks) {
        if (isTaskReady(task, current)) {
            executeTask(task);
        }
    }
    
    // Handle low battery conditions
    if (batteryLevel <= lowPowerThreshold) {
        initiateCharging();
    }
}
```

#### Adaptive Behavior
- **Learning Algorithms**: Route optimization based on usage patterns
- **Environmental Adaptation**: Behavior adjustment for different environments
- **Predictive Maintenance**: Proactive system health monitoring
- **Performance Analytics**: Usage statistics and optimization recommendations

### 13. Integration Capabilities

#### Third-Party Integration
- **Building Management Systems**: HVAC and lighting coordination
- **Security Systems**: Access control and monitoring integration
- **Facility Management**: Maintenance scheduling and reporting
- **IoT Platforms**: Sensor data aggregation and analysis

#### API Exposure
```java
// External integration interfaces
public interface RobotControlAPI {
    void startTask(String taskName);
    void stopCurrentTask();
    SystemStatus getStatus();
    void navigateToPoint(String pointName);
    void setDisinfectionParameters(DisinfectionConfig config);
}
```

## User Experience Features

### Intuitive Interface Design
- **Touch-Optimized**: 7-inch display with responsive touch controls
- **Visual Feedback**: Real-time status indicators and progress displays
- **Contextual Help**: Built-in guidance and troubleshooting tips
- **Accessibility**: Support for different user skill levels

### Operational Efficiency
- **One-Touch Operations**: Simplified common task execution
- **Batch Processing**: Multiple task scheduling and execution
- **Quick Setup**: Streamlined initial configuration process
- **Maintenance Alerts**: Proactive system care notifications

### Reporting and Analytics
- **Task Reports**: Detailed completion status and coverage analysis
- **Performance Metrics**: Efficiency and effectiveness measurements
- **Historical Data**: Long-term operation trends and patterns
- **Compliance Documentation**: Regulatory reporting capabilities
