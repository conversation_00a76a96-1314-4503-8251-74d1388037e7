# Robot Overview

## Introduction

The REEMAN Circular Chassis Navigation Robot is an autonomous disinfection robot designed for indoor environments. It combines advanced SLAM navigation technology with UV disinfection capabilities, running on a dual-platform architecture with Linux-based navigation system and Android control interface.

## Product Specifications

### Physical Dimensions
- **Size**: 450mm (L) × 450mm (W) × 317mm (H)
- **Weight**: 36kg
- **Color**: White
- **Material**: Cold rolled steel sheet

### Environmental Requirements
- **Operating Temperature**: -10°C to +50°C
- **Storage Temperature**: -15°C to +55°C
- **Humidity**: 10% to 90% RH
- **Maximum Slope**: 5°
- **Minimum Passage Width**: 80cm

### Environmental Limitations
⚠️ **Do not use in the following conditions:**
- Areas with stairs or drops > 2cm
- Surfaces with high friction (thick, soft carpets)
- Bathrooms or high-humidity environments
- Areas with dense thin-legged furniture
- Uneven surfaces
- Areas with extensive black marble or reflective surfaces at 30cm height
- Environments above 50°C or below 0°C

## Core Capabilities

### Navigation System
- **SLAM Technology**: Real-time mapping and localization
- **LiDAR**: 270° scanning range, 25-meter detection distance
- **Laser Wavelength**: 905nm
- **Navigation Speed**: 0.1 m/s to 1.0 m/s (adjustable)
- **Positioning Accuracy**: Sub-meter precision with QR code assistance

### Disinfection Features
- **UV Disinfection**: Automated UV light system
- **Task Scheduling**: Programmable disinfection routines
- **Safety Systems**: Emergency stop and obstacle avoidance
- **Coverage Mapping**: Ensures complete area coverage

### Mobility
- **Drive System**: 5.5-inch hub motors
- **Steering**: Differential drive with universal wheels
- **Obstacle Avoidance**: Multi-sensor fusion (LiDAR, ultrasonic, infrared)
- **Emergency Stop**: Physical emergency stop button

## Power System

### Battery Specifications
- **Type**: Lithium battery
- **Voltage**: 37V
- **Capacity**: 10Ah (20Ah optional)
- **Charging**: Automatic docking or direct charging
- **Low Power Threshold**: Configurable (default 20%)

### Charging System
- **Automatic Charging**: Self-docking capability
- **Direct Charging**: Manual DC charging port
- **Charging Pile**: 42VDC, 3A (5A optional)
- **Adapter**: AC110-240V input, DC42V-3A output

## Sensor Suite

### Primary Sensors
- **LiDAR**: 270° laser scanner for navigation
- **IMU**: Single-axis gyroscope for orientation
- **Infrared Sensors**: Charging dock detection (1-meter range)
- **Ultrasonic Sensors**: Close-range obstacle detection

### Vision System
- **QR Code Camera**: 5MP camera for positioning assistance
- **Detection Range**: 1-3m or 1-5m (configurable)
- **Infrared Vision**: Optional infrared camera module

## Connectivity

### Wireless Communication
- **WiFi**: Dual-band 2.4GHz & 5GHz (802.11b/g/n/ac)
- **Bluetooth**: BT 4.1 support
- **Network**: RJ45 Ethernet port
- **4G**: Optional 4G router with external antennas

### Control Interfaces
- **Web Interface**: Browser-based control and monitoring
- **Android App**: Dedicated control application
- **API**: RESTful web API for integration
- **Serial Communication**: RS232/USB for hardware integration

## Operating Modes

### Navigation Mode
- Normal operation mode for autonomous navigation
- Executes programmed tasks and routes
- Real-time obstacle avoidance and path planning

### Mapping Mode
- Map building and environment scanning
- Manual or remote-controlled movement
- Real-time map visualization and editing

### Charging Mode
- Automatic return to charging station
- Low battery detection and response
- Charging status monitoring

## Safety Features

### Emergency Systems
- **Emergency Stop Button**: Immediate halt of all operations
- **Obstacle Detection**: Multi-sensor collision avoidance
- **Virtual Walls**: Software-defined restricted areas
- **Speed Limiting**: Configurable maximum speeds

### Operational Safety
- **Noise Levels**: 45dB at rest, 50-75dB in motion
- **Fail-Safe Design**: Safe shutdown on system errors
- **Remote Monitoring**: Real-time status and diagnostics

## Use Cases

### Primary Applications
- **Healthcare Facilities**: Hospitals, clinics, medical centers
- **Office Buildings**: Corporate offices, co-working spaces
- **Educational Institutions**: Schools, universities, training centers
- **Public Spaces**: Lobbies, waiting areas, corridors
- **Multi-Floor Buildings**: With elevator integration capability

### Operational Benefits
- **24/7 Operation**: Autonomous scheduling and operation
- **Consistent Coverage**: Systematic disinfection patterns
- **Reduced Human Exposure**: Minimizes staff exposure to pathogens
- **Documentation**: Complete logs of disinfection activities
- **Cost Effective**: Reduces manual labor requirements
