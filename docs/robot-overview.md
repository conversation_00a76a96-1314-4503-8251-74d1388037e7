# Robot Overview

## Introduction

The REEMAN Circular Chassis Navigation Robot is an autonomous disinfection robot designed for indoor environments. It combines advanced SLAM navigation technology with UV disinfection capabilities, running on a dual-platform architecture with Linux-based navigation system and Android control interface.

## Product Specifications

### Physical Dimensions
- **Size**: 500mm (L) × 500mm (W) × 310mm (H) *(Moon Knight 2.0 chassis)*
- **Alternative Size**: 450mm (L) × 450mm (W) × 317mm (H) *(Circular chassis variant)*
- **Weight**: 34kg (chassis) + 36kg (complete system with disinfection equipment)
- **Payload Capacity**: Up to 60kg maximum load capacity
- **Color**: White
- **Material**: Cold rolled steel sheet with sheet metal structure

### Environmental Requirements
- **Operating Temperature**: -10°C to +50°C
- **Storage Temperature**: -15°C to +55°C
- **Humidity**: 10% to 90% RH
- **Maximum Slope**: 5°
- **Minimum Passage Width**: 80cm

### Environmental Limitations
⚠️ **Do not use in the following conditions:**
- Areas with stairs or drops > 2cm
- Surfaces with high friction (thick, soft carpets)
- Bathrooms or high-humidity environments
- Areas with dense thin-legged furniture
- Uneven surfaces
- Areas with extensive black marble or reflective surfaces at 30cm height
- Environments above 50°C or below 0°C

## Core Capabilities

### Navigation System
- **SLAM Technology**: SLAM 2.0 autonomous positioning and navigation system
- **LiDAR**: Single-line 270° scanning range, 25-meter detection distance
- **Laser Wavelength**: 905nm
- **Navigation Speed**: 0.1 m/s to 1.0 m/s (adjustable)
- **Positioning Accuracy**: ±10mm precision (no QR code required for Moon Knight 2.0)
- **Alternative Positioning**: QR code assistance available for enhanced precision (1-5m range)

### Disinfection Features
- **UV Disinfection**: Automated UV light system
- **Task Scheduling**: Programmable disinfection routines
- **Safety Systems**: Emergency stop and obstacle avoidance
- **Coverage Mapping**: Ensures complete area coverage

### Mobility
- **Drive System**: 5.5-inch hub motors
- **Steering**: Differential drive with universal wheels
- **Obstacle Avoidance**: Multi-sensor fusion (LiDAR, ultrasonic, infrared)
- **Emergency Stop**: Physical emergency stop button

## Power System

### Battery Specifications
- **Type**: LiFePO4 (Lithium Iron Phosphate) - safer, non-explosive, non-combustible
- **Voltage**: 25.6V (Moon Knight 2.0) / 37V (Circular chassis variant)
- **Capacity**: 25Ah (Moon Knight 2.0) / 10Ah-20Ah (Circular chassis variant)
- **Battery Life**: 15-20 hours with 30kg load, >2000 discharge cycles
- **Charging**: Automatic docking or direct charging
- **Low Power Threshold**: Configurable (default 20%)

### Charging System
- **Automatic Charging**: Self-docking capability with automatic return
- **Direct Charging**: Manual DC charging port
- **Charging Time**: 4 hours (Moon Knight 2.0) / 2-3 hours (Circular chassis)
- **Charging Pile**: 42VDC, 3A (5A optional) / 28.4V-7A (Moon Knight 2.0)
- **Adapter**: AC110-240V input, multiple output configurations

## Sensor Suite

### Primary Sensors
- **LiDAR**: 270° laser scanner for navigation
- **IMU**: Single-axis gyroscope for orientation
- **Infrared Sensors**: Charging dock detection (1-meter range)
- **Ultrasonic Sensors**: Close-range obstacle detection

### Vision System
- **3D Cameras**: Dual 3D cameras for comprehensive environment perception (Moon Knight 2.0)
- **QR Code Camera**: 5MP camera for positioning assistance (optional)
- **Detection Range**: 1-3m or 1-5m (configurable)
- **Infrared Vision**: Optional infrared camera module
- **Obstacle Detection**: Laser SLAM + 3D camera fusion technology

## Connectivity

### Wireless Communication
- **WiFi**: Dual-band 2.4GHz & 5GHz (802.11b/g/n/ac)
- **Bluetooth**: BT 4.1 support
- **Network**: RJ45 Ethernet port
- **4G**: Optional 4G router with external antennas

### Control Interfaces
- **Web Interface**: Browser-based control and monitoring
- **Android App**: Dedicated control application
- **API**: RESTful web API for integration
- **Serial Communication**: RS232/USB for hardware integration

## Operating Modes

### Navigation Mode
- Normal operation mode for autonomous navigation
- Executes programmed tasks and routes
- Real-time obstacle avoidance and path planning

### Mapping Mode
- Map building and environment scanning
- Manual or remote-controlled movement
- Real-time map visualization and editing

### Charging Mode
- Automatic return to charging station
- Low battery detection and response
- Charging status monitoring

## Safety Features

### Emergency Systems
- **Emergency Stop Button**: Immediate halt of all operations
- **Obstacle Detection**: Multi-sensor collision avoidance
- **Virtual Walls**: Software-defined restricted areas
- **Speed Limiting**: Configurable maximum speeds

### Operational Safety
- **Noise Levels**: 45dB at rest, 50-75dB in motion
- **Fail-Safe Design**: Safe shutdown on system errors
- **Remote Monitoring**: Real-time status and diagnostics

## Use Cases

### Primary Applications
- **Healthcare Facilities**: Hospitals, clinics, medical centers
- **Office Buildings**: Corporate offices, co-working spaces
- **Educational Institutions**: Schools, universities, training centers
- **Public Spaces**: Lobbies, waiting areas, corridors
- **Multi-Floor Buildings**: With elevator integration capability

### Operational Benefits
- **24/7 Operation**: Autonomous scheduling and operation
- **Consistent Coverage**: Systematic disinfection patterns
- **Reduced Human Exposure**: Minimizes staff exposure to pathogens
- **Documentation**: Complete logs of disinfection activities
- **Cost Effective**: Reduces manual labor requirements
