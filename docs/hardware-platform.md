# Hardware Platform

## System Architecture

The REEMAN robot employs a dual-processor architecture combining a Raspberry Pi-based navigation system with an Android control interface for optimal performance and user interaction.

## Raspberry Pi Navigation System

### Primary Computing Platform
- **Operating System**: Linux (ROS-based navigation system)
- **Processor**: Intel Core i5 motherboard (navigation board)
- **Storage**: 32GB high-speed solid-state drive
- **Real-time Processing**: SLAM navigation and sensor fusion

### Navigation Hardware
- **LiDAR Unit**: 905nm laser wavelength, 270° field of view
- **IMU Sensor**: Single-axis gyroscope for yaw angle measurement
- **Processing**: Real-time mapping and localization algorithms
- **Communication**: Serial interface to Android system

## Android Control System

### Android Specifications
- **Operating System**: Android 5.1
- **Processor**: RK3128 (ARM-based)
- **Memory**: 1GB LPDDR3 RAM
- **Storage**: 8GB NAND Flash
- **Display**: 7-inch IPS screen (1024×600 resolution, 16:9 aspect ratio)

### User Interface
- **Touch Screen**: Capacitive touch interface
- **Language Support**: Chinese and English
- **Application**: Custom disinfection robot control app (v3.2.5)
- **Real-time Monitoring**: Navigation status and system diagnostics

## Sensor Integration

### Primary Sensors
```
┌─────────────────┐    ┌─────────────────┐
│   LiDAR Unit    │    │  QR Code Camera │
│   270° Scan     │    │   5MP, 1-5m     │
│   25m Range     │    │   Positioning   │
└─────────────────┘    └─────────────────┘
         │                       │
         └───────┬───────────────┘
                 │
    ┌─────────────────────────────┐
    │    Raspberry Pi System      │
    │   (Navigation Processing)   │
    └─────────────────────────────┘
                 │
    ┌─────────────────────────────┐
    │     Android System          │
    │   (User Interface & App)    │
    └─────────────────────────────┘
```

### Sensor Specifications
- **Infrared Sensors**: Charging dock detection within 1-meter range
- **Ultrasonic Sensors**: Close-range obstacle detection
- **IMU**: Orientation and movement tracking
- **Vision Camera**: Optional infrared vision module

## Power Management

### Battery System
- **Primary Battery**: 37V/10Ah lithium battery (20Ah optional)
- **Power Distribution**: Intelligent power management system
- **Protection**: Overcurrent protection and intelligent power-off
- **Monitoring**: Real-time battery level and charging status

### Charging Infrastructure
- **Automatic Charging**: Self-docking with charging pile
- **Direct Charging**: DC input port for manual charging
- **Charging Pile**: 42VDC output, 3A rated current (5A optional)
- **Safety**: Automatic charging detection and management

## Communication Systems

### Wireless Connectivity
- **WiFi Module**: Intel 512AN_HMW
  - Dual-band: 2.4GHz & 5GHz
  - Standards: 802.11b/g/n/ac
  - Frequency Range: 5.15GHz-5.825GHz
- **Bluetooth**: BT 4.1 support
- **4G Option**: External 4G router with antenna system

### Wired Interfaces
- **Ethernet**: RJ45 network port
- **USB**: Micro USB debug port, USB 2.0 port
- **Serial**: RS232/TTL for hardware communication
- **Power**: DC charging input

## Motor and Drive System

### Locomotion
- **Drive Motors**: 5.5-inch hub motors (dual-wheel drive)
- **Steering**: Differential drive system
- **Support Wheels**: Universal wheels for stability
- **Speed Control**: Variable speed 0.1-1.0 m/s

### Motor Specifications
- **Type**: Brushless DC hub motors
- **Control**: PWM speed control
- **Feedback**: Encoder feedback for precise positioning
- **Safety**: Emergency stop integration

## External Device Integration

### Serial Port Configuration
```java
// Default serial port settings
String serialPort = "/dev/ttyS1";  // ROS communication
int baudRate = 115200;             // Communication speed

// Additional ports for peripherals
String gatingPort = "/dev/ttyUSB0";    // Access control
String elevatorPort = "/dev/ttyUSB1";  // Elevator control
```

### Peripheral Connections
- **Elevator Control**: Serial interface for elevator integration
- **Access Control**: Door/gate control systems
- **External Sensors**: Expandable sensor integration
- **Disinfection Equipment**: UV light control systems

## Hardware Layout

### Component Placement
```
    ┌─────────────────────────────────┐
    │           LiDAR Unit            │
    │              ┌─┐                │
    │              │ │                │
    │    ┌─────────┴─┴─────────┐      │
    │    │   Android Display   │      │
    │    │     (Touch Screen)  │      │
    │    └─────────────────────┘      │
    │                                 │
    │  ┌─────┐              ┌─────┐   │
    │  │Motor│              │Motor│   │
    │  └─────┘              └─────┘   │
    │                                 │
    │    ┌─────────────────────┐      │
    │    │   Battery Pack      │      │
    │    └─────────────────────┘      │
    │                                 │
    │  [Emergency Stop]  [Power]      │
    └─────────────────────────────────┘
```

### Physical Interfaces
- **Emergency Stop**: Twist-to-release safety button
- **Power Button**: Main system power control
- **Charging Contacts**: Automatic charging connection points
- **USB Ports**: Debug and data transfer
- **Network Port**: Ethernet connection

## Environmental Considerations

### Operating Conditions
- **Temperature Range**: -10°C to +50°C operational
- **Humidity**: 10% to 90% RH
- **Dust Protection**: IP-rated enclosures for critical components
- **Vibration**: Shock-mounted sensitive components

### Maintenance Access
- **Modular Design**: Easy access to key components
- **Diagnostic Ports**: Built-in diagnostic interfaces
- **Component Replacement**: Field-replaceable units
- **Firmware Updates**: Over-the-air update capability

## Performance Specifications

### Processing Performance
- **Navigation Processing**: Real-time SLAM at 10Hz
- **Sensor Fusion**: Multi-sensor data integration
- **Path Planning**: Dynamic obstacle avoidance
- **Response Time**: <100ms for emergency stops

### Communication Performance
- **WiFi Range**: Up to 100m in open areas
- **Data Throughput**: Real-time video and telemetry
- **Latency**: <50ms for control commands
- **Reliability**: Automatic reconnection and failover

## Integration Capabilities

### Third-Party Integration
- **API Access**: RESTful web services
- **SDK Support**: Android development kit
- **Protocol Support**: Standard communication protocols
- **Custom Hardware**: Expandable I/O interfaces

### System Monitoring
- **Health Monitoring**: Continuous system diagnostics
- **Performance Metrics**: Real-time performance data
- **Alert Systems**: Automated fault detection
- **Remote Diagnostics**: Network-based troubleshooting
