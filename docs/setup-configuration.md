# Setup and Configuration

## Initial Setup

### 1. Hardware Setup

#### Power System Configuration
##### Moon Knight 2.0 Chassis
1. **Battery Installation**: Ensure LiFePO4 25.6V/25Ah battery is properly connected
2. **Charging Setup**: Position charging station for 28.4V-7A charging system
3. **Extended Operation**: Verify 15-20 hour operation capability with load testing

##### Circular Chassis Platform
1. **Battery Installation**: Ensure 37V lithium battery is properly connected
2. **Charging Pile Setup**: Position charging pile in accessible location

##### Universal Power Controls
3. **Power Button**: Located on the back of the robot base
   - Press once to power on
   - Press and hold for 3 seconds to power off

#### Physical Placement Requirements
##### Moon Knight 2.0 Chassis (500×500×310mm)
- Ensure minimum 80cm clearance for passages (larger footprint consideration)
- Maximum 60kg payload capacity - verify floor load ratings
- No QR code positioning required (SLAM 2.0 navigation)
- Dual 3D camera clear line of sight requirements

##### Circular Chassis Platform (450×450×317mm)
- Ensure minimum 80cm clearance for passages
- Maintain clear ceiling access for QR code positioning (1-5m height)
- Optimized for disinfection coverage patterns

##### Universal Requirements
- Avoid areas with stairs or drops > 2cm
- Position away from black marble or reflective surfaces at 30cm height
- Consider multi-robot coordination spacing for fleet operations

### 2. Network Configuration

#### WiFi Connection
1. **Android Interface Setup**:
   ```
   Settings → WLAN → Connect to WiFi network
   ```

2. **ROS System WiFi Configuration**:
   - Open FTP application on Android screen
   - Select desired WiFi network
   - Enter WiFi password
   - Click "Send WiFi information to connect ROS"
   - Verify connection (IP address displayed, not 127.0.0.1)

#### Network Requirements
- Robot and control devices must be on same LAN
- Avoid 10.x.x.x network segments
- Recommended: 192.168.x.x or 172.16.x.x networks
- Ensure stable WiFi signal throughout operating area

### 3. Camera Configuration

#### Infrared Vision Camera Setup
1. **Physical Installation**:
   - Camera interface faces forward (toward two red circles)
   - Line must be straight ahead of machine
   - Position must be horizontal for proper QR code positioning

2. **Parameter Configuration**:
   ```
   Access: http://[robot_ip] → Double-click 'laser' position
   
   Robot Type: Select 'sterilizing robot'
   Camera Settings:
   - X: Distance from wheel center (front/back)
   - Y: Distance from wheel center (left/right)  
   - Z: Camera height from ground
   
   Example:
   X: -0.123 (backward from wheel center)
   Y: 0 (centered)
   Z: 1.208 (height in meters)
   ```

#### 4G Router Antenna Setup (Optional)
- Install main and sub antennas on plastic parts (not metal)
- Avoid metal shielding areas
- Can be mounted on infrared camera module shell

## Map Building Process

### 1. Preparation
- Ensure robot is in open area
- Clear temporary obstacles from environment
- Verify all sensors are functioning
- Check battery level (>50% recommended)

### 2. Enter Mapping Mode
```
Web Interface: http://[robot_ip]
Navigation: Get IP → Enter mapping interface
Mode: Select "Build Map"
```

### 3. Map Construction
1. **Initial Rotation**: Robot automatically rotates to clear surrounding features
2. **Manual Control Options**:
   - Push robot manually (emergency stop engaged)
   - Use keyboard arrow keys for remote control
   - Use web interface directional controls

3. **Mapping Strategy**:
   ```
   Recommended Pattern:
   ┌─────────────────────┐
   │ ←←←←←←←←←←←←←←←←←←← │
   │                     ↓
   │ →→→→→→→→→→→→→→→→→→→ │
   │ ↑                   ↓
   │ →→→→→→→→→→→→→→→→→→→ │
   │                     ↓
   │ ←←←←←←←←←←←←←←←←←←← │
   └─────────────────────┘
   ```

4. **Critical Areas**:
   - **Narrow passages**: Move slowly, ensure 90° turns to clear features
   - **Open areas**: Use U-shaped pattern for complete coverage
   - **Corners**: Rotate in place to scan all angles
   - **Doorways**: Ensure complete threshold scanning

### 4. Map Validation
1. **Real-time Monitoring**: Watch laser alignment with actual terrain
2. **Mismatch Handling**: Stop and wait if laser doesn't match environment
3. **Completion Check**: Verify map is clean without ghosting or dislocation
4. **Final Save**: Click "Composition Completed" when satisfied

### 5. Map Quality Assurance
- **No Ghosting**: Overlapping or duplicate features
- **Proper Alignment**: Walls and obstacles correctly positioned
- **Complete Coverage**: All accessible areas mapped
- **Feature Clarity**: Clear distinction between obstacles and free space

## Virtual Wall Configuration

### Purpose
Virtual walls restrict robot movement areas and define operational boundaries.

### Drawing Tools
- **Drag Mode**: Pan, zoom, rotate map view
- **Draw Curve**: Create irregular boundary lines
- **Draw Straight Line**: Click two points for straight boundaries
- **Eraser**: Remove unwanted virtual walls
- **Clear All**: Remove all virtual walls (requires save)

### Best Practices
1. **Minimum Clearance**: Maintain 80cm passage width
2. **Glass Walls**: Draw virtual walls outside glass surfaces (laser penetrates glass)
3. **Table Areas**: Consider desktop projection when drawing boundaries
4. **Safety Zones**: Block access to stairs, drops, or hazardous areas

### Examples
```
Glass Wall Handling:
Actual: [Wall]|Glass|[Room]
Laser:  [Wall]      [Room]  (sees through glass)
Virtual:[Wall]|XXXX|[Room]  (block with virtual wall)

Table Handling:
Actual: [Floor][Table Legs][Floor]
Laser:  [Floor]           [Floor]  (passes under table)
Virtual:[Floor]|XXXXXXXX|[Floor]   (block table area)
```

## Position Calibration

### Charging Pile Calibration
1. **Physical Positioning**: Move robot 1m directly in front of charging pile
2. **Alignment Check**: Verify charging contacts face each other
3. **Position Verification**: Confirm orange laser aligns with black obstacles on map
4. **Calibration**: Use "Current Position" button, name as "charging_pile" (exact name required)

### Navigation Points
1. **Point Selection**: Click walkable (white) areas on map
2. **Naming Convention**: Use sequential numbers (1, 2, 3...) for disinfection app compatibility
3. **Clearance Requirements**: Minimum 50cm from obstacles and virtual walls
4. **Strategic Placement**: Position points for optimal coverage patterns

### QR Code Positioning

#### Deployment Requirements
- **Ceiling Height**: 1-5 meters from camera
- **Surface**: Flat, non-reflective ceiling
- **Positioning**: Center of pathways, away from obstacles
- **Spacing**: 5-10 meters apart, mandatory at corners and intersections

#### Installation Process
1. **Preparation**: Clean ceiling surface
2. **Positioning**: Center QR code in pathway
3. **Application**: Remove white backing, apply transparent side to ceiling
4. **Finishing**: Remove transparent layer, ensure circular spots remain intact

#### Calibration Process
1. **Robot Positioning**: Navigate robot directly under QR code
2. **Verification**: Confirm accurate robot positioning on map
3. **Label Type Selection**:
   - **Common Label (C-)**: Standard positioning assistance
   - **Forbidden Zone (J-)**: Emergency stop trigger for dangerous areas

## System Configuration

### Navigation Parameters
```
Web Interface Settings:
- Maximum Speed: 0.3 - 1.0 m/s
- Navigation Mode: Standard/Precision
- Global Radius: Obstacle avoidance distance
- Low Power Threshold: 10-30% (default 20%)
```

### Android App Settings
```
Application Configuration:
- Voice Broadcast: Enable/Disable
- Screen Lock: Security settings
- Language: Chinese/English
- Volume: System audio level
- Delay Time: Task execution delays
```

### Network Settings
```
Connection Configuration:
- WiFi: Primary network connection
- Ethernet: Backup wired connection
- 4G Router: Optional cellular backup
- Port Mapping: Custom port configurations
```

## Special Area Configuration

### Purpose
Optimize navigation in corridors and areas with limited features.

### When to Use
- **Long Corridors**: >10 meters with minimal features
- **Open Halls**: Large spaces with few landmarks
- **Uniform Areas**: Repetitive environments

### Configuration Process
1. **Mode Selection**: Switch to special area drawing mode
2. **Area Definition**: Click points to form polygon around area
3. **Boundary Rules**: 
   - Don't cover corridor ends
   - Maintain 2-meter clearance from ends
   - Focus on feature-poor sections
4. **Save**: Apply special area configuration

## Troubleshooting Setup Issues

### Network Connection Problems
```
Symptoms: 127.0.0.1 displayed, no web access
Solutions:
1. Verify WiFi password accuracy
2. Check network compatibility (avoid 10.x.x.x)
3. Restart WiFi connection process
4. Try alternative network
5. Check router settings and firewall
```

### Mapping Issues
```
Symptoms: Ghosting, misalignment, incomplete maps
Solutions:
1. Slow down mapping speed
2. Ensure proper lighting conditions
3. Remove temporary obstacles
4. Check sensor cleanliness
5. Restart mapping process if necessary
```

### Camera Positioning Problems
```
Symptoms: QR code detection failures
Solutions:
1. Verify camera orientation (forward-facing)
2. Check camera height parameters
3. Ensure horizontal mounting
4. Clean camera lens
5. Recalibrate camera settings
```

### Charging Issues
```
Symptoms: Charging pile not found, docking failures
Solutions:
1. Verify "charging_pile" point naming
2. Check charging pile positioning
3. Ensure clear approach path
4. Verify infrared sensor functionality
5. Clean charging contacts
```

## Maintenance Schedule

### Daily Checks
- Battery level and charging status
- Sensor cleanliness (LiDAR, cameras)
- Emergency stop functionality
- Network connectivity

### Weekly Maintenance
- Map accuracy verification
- Virtual wall effectiveness
- QR code condition and adhesion
- Software update checks

### Monthly Service
- Deep sensor cleaning
- Battery health assessment
- Mechanical component inspection
- Performance optimization review
